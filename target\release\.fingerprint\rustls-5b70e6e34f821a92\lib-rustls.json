{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 15818247454983744812, "path": 4620867225528765084, "deps": [[1542112352204983347, "build_script_build", false, 5055744677063061454], [2883436298747778685, "pki_types", false, 5141792932089840179], [3722963349756955755, "once_cell", false, 4623307841458457968], [5491919304041016563, "ring", false, 12843157879976403154], [6528079939221783635, "zeroize", false, 10788607200797166922], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 7301219531263802324], [17003143334332120809, "subtle", false, 12209182072914445335]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-5b70e6e34f821a92\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}