{"rustc": 1842507548689473721, "features": "[\"rustls-connector\", \"rustls-native-certs\"]", "declared_features": "[\"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-connector\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\"]", "target": 11924301528678068555, "profile": 3592778941406178886, "path": 13840485830793618560, "deps": [[8606274917505247608, "tracing", false, 11372811433317039271], [11096876330329401515, "amq_protocol_uri", false, 4014177116918990465], [17059544261156971941, "tcp_stream", false, 11741282304099387937]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\amq-protocol-tcp-8897eae8cfda0b72\\dep-lib-amq_protocol_tcp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}