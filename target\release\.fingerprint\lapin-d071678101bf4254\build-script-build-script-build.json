{"rustc": 1842507548689473721, "features": "[\"default\", \"rustls\", \"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"serde_json\", \"vendored-openssl\"]", "target": 5408242616063297496, "profile": 9719146384424564959, "path": 2950602454442530859, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\lapin-d071678101bf4254\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}