// 工具模块 - 去重服务和其他工具函数
use crate::config::RedisConfig;
use crate::errors::{CallbackError, CallbackResult};
use redis::{AsyncCommands, Client as RedisClient, cmd};
use std::time::Duration;
use tracing::{info, warn, error, debug};
use uuid::Uuid;

/// Redis去重服务 - 防止重复回调的核心组件
#[derive(Debug, Clone)]
pub struct DeduplicationService {
    redis_client: RedisClient,
    ttl: Duration,
}

impl DeduplicationService {
    pub async fn new(config: &RedisConfig, ttl: Duration) -> CallbackResult<Self> {
        info!("初始化Redis去重服务...");
        
        let redis_client = RedisClient::open(config.connection_url.as_str())?;
        
        // 测试连接
        let mut conn = redis_client.get_async_connection().await?;
        let _: String = cmd("PING").query_async(&mut conn).await?;
        
        info!("Redis去重服务初始化成功");
        
        Ok(Self {
            redis_client,
            ttl,
        })
    }
    
    /// 检查是否正在处理中
    pub async fn is_processing(&self, record_id: &str, retry_count: u32) -> CallbackResult<bool> {
        let key = self.build_processing_key(record_id, retry_count);
        
        let mut conn = self.redis_client.get_async_connection().await?;
        let exists: bool = conn.exists(&key).await?;
        
        debug!("检查处理状态: key={}, exists={}", key, exists);
        Ok(exists)
    }
    
    /// 获取处理锁
    pub async fn acquire_processing_lock(
        &self, 
        record_id: &str, 
        retry_count: u32
    ) -> CallbackResult<ProcessingGuard> {
        let key = self.build_processing_key(record_id, retry_count);
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // 使用SET NX EX命令原子性设置锁
        let lock_value = format!("{}:{}", std::process::id(), Uuid::new_v4());
        let set_result: bool = conn.set_nx(&key, &lock_value).await?;
        
        if !set_result {
            debug!("无法获取处理锁: key={}", key);
            return Err(CallbackError::duplicate_processing(record_id.to_string(), retry_count));
        }
        
        // 设置过期时间
        conn.expire(&key, self.ttl.as_secs() as i64).await?;
        
        info!("获取处理锁成功: key={}, value={}", key, lock_value);
        
        Ok(ProcessingGuard {
            redis_client: self.redis_client.clone(),
            key,
            lock_value,
        })
    }
    
    /// 检查通知是否已发送
    pub async fn is_notification_sent(
        &self, 
        record_id: &str, 
        error_hash: &str
    ) -> CallbackResult<bool> {
        let key = self.build_notification_key(record_id, error_hash);
        
        let mut conn = self.redis_client.get_async_connection().await?;
        let exists: bool = conn.exists(&key).await?;
        
        debug!("检查通知状态: key={}, exists={}", key, exists);
        Ok(exists)
    }
    
    /// 标记通知已发送
    pub async fn mark_notification_sent(
        &self, 
        record_id: &str, 
        error_hash: &str
    ) -> CallbackResult<()> {
        let key = self.build_notification_key(record_id, error_hash);
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // 设置标记，10分钟过期
        conn.set_ex(&key, "1", 600).await?;
        
        debug!("标记通知已发送: key={}", key);
        Ok(())
    }
    
    /// 清理过期的锁（可选的维护操作）
    pub async fn cleanup_expired_locks(&self) -> CallbackResult<u32> {
        let pattern = "callback_processing:*";
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let keys: Vec<String> = conn.keys(pattern).await?;
        let mut cleaned = 0;
        
        for key in keys {
            let ttl: i64 = conn.ttl(&key).await?;
            if ttl == -1 {
                // 没有过期时间的key，删除它
                let deleted: u32 = conn.del(&key).await?;
                cleaned += deleted;
            }
        }
        
        if cleaned > 0 {
            info!("清理过期锁: 清理了 {} 个key", cleaned);
        }
        
        Ok(cleaned)
    }
    
    /// 健康检查
    pub async fn health_check(&self) -> CallbackResult<bool> {
        let mut conn = self.redis_client.get_async_connection().await;
        
        match conn {
            Ok(mut c) => {
                let result: Result<String, _> = cmd("PING").query_async(&mut c).await;
                Ok(result.is_ok())
            }
            Err(_) => Ok(false),
        }
    }
    
    /// 构建处理锁的key
    fn build_processing_key(&self, record_id: &str, retry_count: u32) -> String {
        format!("callback_processing:{}:retry_{}", record_id, retry_count)
    }
    
    /// 构建通知锁的key
    fn build_notification_key(&self, record_id: &str, error_hash: &str) -> String {
        format!("callback_notification:{}:{}", record_id, error_hash)
    }
}

/// RAII模式的处理锁守卫
pub struct ProcessingGuard {
    redis_client: RedisClient,
    key: String,
    lock_value: String,
}

impl ProcessingGuard {
    /// 手动释放锁
    pub async fn release(self) -> CallbackResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // 使用Lua脚本确保只删除自己的锁
        let script = r#"
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
        "#;
        
        let result: u32 = redis::Script::new(script)
            .key(&self.key)
            .arg(&self.lock_value)
            .invoke_async(&mut conn)
            .await?;
            
        if result > 0 {
            debug!("手动释放处理锁: key={}", self.key);
        } else {
            warn!("锁已被其他进程释放或过期: key={}", self.key);
        }
        
        Ok(())
    }
}

impl Drop for ProcessingGuard {
    fn drop(&mut self) {
        let redis_client = self.redis_client.clone();
        let key = self.key.clone();
        let lock_value = self.lock_value.clone();
        
        // 异步清理锁
        tokio::spawn(async move {
            if let Ok(mut conn) = redis_client.get_async_connection().await {
                let script = r#"
                    if redis.call("GET", KEYS[1]) == ARGV[1] then
                        return redis.call("DEL", KEYS[1])
                    else
                        return 0
                    end
                "#;
                
                let result: Result<u32, _> = redis::Script::new(script)
                    .key(&key)
                    .arg(&lock_value)
                    .invoke_async(&mut conn)
                    .await;
                    
                match result {
                    Ok(deleted) if deleted > 0 => {
                        debug!("自动释放处理锁: key={}", key);
                    }
                    Ok(_) => {
                        debug!("锁已被其他进程释放或过期: key={}", key);
                    }
                    Err(e) => {
                        error!("释放处理锁失败: key={}, error={}", key, e);
                    }
                }
            }
        });
    }
}

/// 工具函数
pub mod helpers {
    use sha2::{Sha256, Digest};
    use std::time::Duration;
    
    /// 计算字符串的SHA256哈希
    pub fn hash_string(input: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(input.as_bytes());
        format!("{:x}", hasher.finalize())
    }
    
    /// 计算错误消息的短哈希（用于去重）
    pub fn hash_error_message(error_msg: &str) -> String {
        let full_hash = hash_string(error_msg);
        // 取前8位作为短哈希
        full_hash[..8].to_string()
    }
    
    /// 计算指数退避延迟
    pub fn calculate_backoff_delay(retry_count: u32, base_delay: Duration, max_delay: Duration) -> Duration {
        let delay_secs = base_delay.as_secs() * 2_u64.pow(retry_count);
        let delay = Duration::from_secs(delay_secs);
        
        if delay > max_delay {
            max_delay
        } else {
            delay
        }
    }
    
    /// 验证URL格式
    pub fn is_valid_url(url: &str) -> bool {
        url::Url::parse(url).is_ok()
    }
    
    /// 生成请求ID
    pub fn generate_request_id() -> String {
        uuid::Uuid::new_v4().to_string()
    }
}

// 添加URL依赖
// 需要在Cargo.toml中添加: url = "2.4"
