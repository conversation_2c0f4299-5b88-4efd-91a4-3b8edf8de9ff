{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 18445720984101308140, "path": 16831464999238497880, "deps": [[1009387600818341822, "matchers", false, 5938841509743209462], [1017461770342116999, "sharded_slab", false, 5411069474576762902], [1359731229228270592, "thread_local", false, 8409181948813167467], [3424551429995674438, "tracing_core", false, 1776836308427570600], [3666196340704888985, "smallvec", false, 73218954786341571], [3722963349756955755, "once_cell", false, 4623307841458457968], [8606274917505247608, "tracing", false, 11372811433317039271], [8614575489689151157, "nu_ansi_term", false, 7861356336387650517], [9451456094439810778, "regex", false, 16875383722942844835], [10806489435541507125, "tracing_log", false, 4767653149268017702]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-dee6d619966912dc\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}