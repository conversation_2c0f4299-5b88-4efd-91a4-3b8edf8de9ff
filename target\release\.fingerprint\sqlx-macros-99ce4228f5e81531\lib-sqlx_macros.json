{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"mysql\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 9719146384424564959, "path": 3151900342374167590, "deps": [[996810380461694889, "sqlx_core", false, 461426786534049534], [2713742371683562785, "syn", false, 6265370072615529970], [3060637413840920116, "proc_macro2", false, 14058059813020485750], [15733334431800349573, "sqlx_macros_core", false, 1410611639079853358], [17990358020177143287, "quote", false, 14183324987747926496]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-macros-99ce4228f5e81531\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}