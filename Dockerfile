# Walmart绑卡回调通知服务 - Docker构建文件

# 构建阶段
FROM rust:1.75-slim as builder

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY Cargo.toml Cargo.lock ./

# 创建虚拟源文件以缓存依赖
RUN mkdir src && echo "fn main() {}" > src/main.rs
RUN cargo build --release && rm -rf src

# 复制源代码
COPY src ./src

# 构建应用
RUN cargo build --release

# 运行阶段
FROM ubuntu:24.04

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libmysqlclient21 \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -r -s /bin/false walmart

# 创建应用目录
RUN mkdir -p /opt/walmart-callback

# 复制构建产物
COPY --from=builder /app/target/release/walmart-bind-card-notify /opt/walmart-callback/
COPY config.toml /opt/walmart-callback/

# 设置权限
RUN chown -R walmart:walmart /opt/walmart-callback
RUN chmod +x /opt/walmart-callback/walmart-bind-card-notify

# 切换到应用用户
USER walmart

# 设置工作目录
WORKDIR /opt/walmart-callback

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /opt/walmart-callback/walmart-bind-card-notify --health-check || exit 1

# 暴露端口（如果有HTTP服务）
# EXPOSE 8080

# 设置环境变量
ENV RUST_LOG=info
ENV CONFIG_PATH=/opt/walmart-callback/config.toml

# 启动应用
CMD ["/opt/walmart-callback/walmart-bind-card-notify"]
