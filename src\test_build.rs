// 构建测试文件 - 验证所有模块是否可以正确编译

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_error_types() {
        let error = CallbackError::business("测试错误");
        assert!(matches!(error, CallbackError::Business { .. }));
        
        let error = CallbackError::duplicate_processing("test_id".to_string(), 1);
        assert!(matches!(error, CallbackError::DuplicateProcessing { .. }));
    }
    
    #[test]
    fn test_config_default() {
        let config = CallbackConfig::default();
        assert_eq!(config.callback.maximum_retry_attempts, 3);
        assert_eq!(config.callback.request_timeout_seconds, 10);
    }
    
    #[test]
    fn test_models() {
        use crate::models::{CallbackStatus, db_constants};
        
        let status = CallbackStatus::Success;
        assert_eq!(status.as_str(), db_constants::CALLBACK_STATUS_SUCCESS);
        
        let status = CallbackStatus::from_str("success");
        assert_eq!(status, Some(CallbackStatus::Success));
    }
    
    #[test]
    fn test_utils_helpers() {
        use crate::utils::helpers;
        use std::time::Duration;
        
        let hash = helpers::hash_string("test");
        assert!(!hash.is_empty());
        
        let error_hash = helpers::hash_error_message("test error");
        assert_eq!(error_hash.len(), 8);
        
        let delay = helpers::calculate_backoff_delay(
            1, 
            Duration::from_secs(2), 
            Duration::from_secs(30)
        );
        assert_eq!(delay, Duration::from_secs(4));
        
        assert!(helpers::is_valid_url("https://example.com"));
        assert!(!helpers::is_valid_url("invalid-url"));
    }
}
