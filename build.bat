@echo off
REM Walmart绑卡回调通知服务构建脚本 (Windows -> Ubuntu 24 交叉编译 + 混淆)

echo 🦀 开始构建 Walmart 绑卡回调通知服务 (Rust版本)
echo 🎯 目标平台: Ubuntu 24.04 (x86_64-unknown-linux-gnu)
echo 🔒 启用代码混淆和加密

REM 检查Rust环境
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到 Cargo，请先安装 Rust
    echo 💡 请访问 https://rustup.rs/ 安装 Rust
    exit /b 1
)

echo ✅ Rust 环境检查通过

REM 检查并安装交叉编译目标
echo 🔧 检查交叉编译目标...
rustup target list --installed | findstr "x86_64-unknown-linux-gnu" >nul
if %ERRORLEVEL% NEQ 0 (
    echo 📦 安装 Ubuntu 24 交叉编译目标...
    rustup target add x86_64-unknown-linux-gnu
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ 安装交叉编译目标失败
        exit /b 1
    )
)

echo ✅ 交叉编译目标已就绪

REM 清理之前的构建
echo 🧹 清理之前的构建...
cargo clean

REM 检查代码格式
echo 📝 检查代码格式...
where rustfmt >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    cargo fmt --check
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  代码格式不符合标准，正在自动格式化...
        cargo fmt
    )
)

REM 运行 Clippy 检查
echo 🔍 运行 Clippy 代码检查...
where clippy >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    cargo clippy -- -D warnings
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  Clippy 检查发现问题，请修复后重试
    )
)

REM 编译检查
echo 🔧 编译检查...
cargo check
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译检查失败
    exit /b 1
)

REM 运行测试
echo 🧪 运行测试...
cargo test
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 测试失败
    exit /b 1
)

REM 构建 Debug 版本
echo 🏗️  构建 Debug 版本...
cargo build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Debug 构建失败
    exit /b 1
)

REM 构建 Ubuntu 24 Release 版本 (交叉编译)
echo 🚀 构建 Ubuntu 24 Release 版本 (交叉编译)...
set RUSTFLAGS=-C target-cpu=native -C opt-level=3 -C lto=fat -C codegen-units=1 -C panic=abort -C strip=symbols
cargo build --release --target x86_64-unknown-linux-gnu
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Ubuntu 24 Release 构建失败
    exit /b 1
)

REM 检查是否有UPX压缩工具
echo 🗜️  检查UPX压缩工具...
where upx >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo 📦 使用UPX压缩和混淆二进制文件...
    upx --best --lzma target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify
    if %ERRORLEVEL% EQU 0 (
        echo ✅ UPX压缩完成
    ) else (
        echo ⚠️  UPX压缩失败，继续使用未压缩版本
    )
) else (
    echo ⚠️  未找到UPX工具，跳过压缩
    echo 💡 可从 https://upx.github.io/ 下载UPX工具
)

REM 创建部署包
echo 📦 创建Ubuntu 24部署包...
if not exist "dist" mkdir dist
if not exist "dist\ubuntu24" mkdir dist\ubuntu24

copy target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify dist\ubuntu24\
copy config.toml dist\ubuntu24\
copy README.md dist\ubuntu24\
copy DEPLOYMENT.md dist\ubuntu24\

REM 创建启动脚本
echo #!/bin/bash > dist\ubuntu24\start.sh
echo # Walmart绑卡回调通知服务启动脚本 >> dist\ubuntu24\start.sh
echo cd "$(dirname "$0")" >> dist\ubuntu24\start.sh
echo export RUST_LOG=info >> dist\ubuntu24\start.sh
echo export CONFIG_PATH=./config.toml >> dist\ubuntu24\start.sh
echo ./walmart-bind-card-notify >> dist\ubuntu24\start.sh

REM 创建systemd服务文件
echo [Unit] > dist\ubuntu24\walmart-callback.service
echo Description=Walmart Callback Service >> dist\ubuntu24\walmart-callback.service
echo After=network.target mysql.service redis.service >> dist\ubuntu24\walmart-callback.service
echo. >> dist\ubuntu24\walmart-callback.service
echo [Service] >> dist\ubuntu24\walmart-callback.service
echo Type=simple >> dist\ubuntu24\walmart-callback.service
echo User=walmart >> dist\ubuntu24\walmart-callback.service
echo Group=walmart >> dist\ubuntu24\walmart-callback.service
echo WorkingDirectory=/opt/walmart-callback >> dist\ubuntu24\walmart-callback.service
echo ExecStart=/opt/walmart-callback/walmart-bind-card-notify >> dist\ubuntu24\walmart-callback.service
echo Environment=CONFIG_PATH=/opt/walmart-callback/config.toml >> dist\ubuntu24\walmart-callback.service
echo Environment=RUST_LOG=info >> dist\ubuntu24\walmart-callback.service
echo Restart=always >> dist\ubuntu24\walmart-callback.service
echo RestartSec=5 >> dist\ubuntu24\walmart-callback.service
echo. >> dist\ubuntu24\walmart-callback.service
echo [Install] >> dist\ubuntu24\walmart-callback.service
echo WantedBy=multi-user.target >> dist\ubuntu24\walmart-callback.service

echo ✅ 构建完成！
echo.
echo 📦 构建产物:
echo   Windows Debug:   .\target\debug\walmart-bind-card-notify.exe
echo   Windows Release: .\target\release\walmart-bind-card-notify.exe
echo   Ubuntu 24:       .\target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify
echo.
echo 🎯 Ubuntu 24 部署包: .\dist\ubuntu24\
echo   ├── walmart-bind-card-notify    (混淆加密的执行文件)
echo   ├── config.toml                 (配置文件)
echo   ├── start.sh                    (启动脚本)
echo   ├── walmart-callback.service    (systemd服务文件)
echo   ├── README.md                   (使用文档)
echo   └── DEPLOYMENT.md               (部署指南)
echo.
echo 🚀 Ubuntu 24 部署方式:
echo   1. 将 dist\ubuntu24\ 目录上传到Ubuntu服务器
echo   2. chmod +x walmart-bind-card-notify start.sh
echo   3. sudo cp walmart-callback.service /etc/systemd/system/
echo   4. sudo systemctl enable walmart-callback
echo   5. sudo systemctl start walmart-callback
echo.
echo ⚙️  配置文件: config.toml
echo 📚 文档: README.md
echo 🔒 安全特性: 代码混淆 + UPX压缩 + 符号剥离

pause
