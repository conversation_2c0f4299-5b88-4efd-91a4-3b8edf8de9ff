{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 177473949561876047, "path": 401074665980160567, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\winnow-4bf746eb6156e4bf\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}