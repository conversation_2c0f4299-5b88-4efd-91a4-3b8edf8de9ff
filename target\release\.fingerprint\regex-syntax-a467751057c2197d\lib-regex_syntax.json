{"rustc": 1842507548689473721, "features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7529137146482485884, "profile": 3592778941406178886, "path": 15943739450476438210, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-syntax-a467751057c2197d\\dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}