// Walmart绑卡回调通知服务 - Rust版本
// 提供高性能、内存安全的回调处理能力

pub mod config;
pub mod models;
pub mod services;
pub mod database;
pub mod queue;
pub mod http;
pub mod errors;
pub mod utils;

// 重新导出主要类型
pub use config::CallbackConfig;
pub use services::CallbackService;
pub use errors::{CallbackError, CallbackResult};

// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");

// 初始化日志
pub fn init_logging() {
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .init();
}

#[cfg(test)]
mod test_build;
