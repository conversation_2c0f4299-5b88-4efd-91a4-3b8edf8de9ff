{"rustc": 1842507548689473721, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 3592778941406178886, "path": 3124027747632419540, "deps": [[555019317135488525, "regex_automata", false, 16003763143508933279], [9408802513701742484, "regex_syntax", false, 15209441719814523536]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-5f4f91abb8ba<PERSON>ce\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}