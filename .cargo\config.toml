# Cargo 构建配置 - 优化混淆和交叉编译

[build]
# 默认目标为 Ubuntu 24
target = "x86_64-unknown-linux-gnu"

[target.x86_64-unknown-linux-gnu]
# 交叉编译链接器 (如果安装了的话)
# linker = "x86_64-linux-gnu-gcc"

[profile.release]
# 发布版本优化配置
opt-level = 3              # 最高优化级别
lto = "fat"               # 链接时优化 (Link Time Optimization)
codegen-units = 1         # 单个代码生成单元 (更好的优化)
panic = "abort"           # panic时直接终止 (减小二进制大小)
strip = "symbols"         # 剥离符号表
overflow-checks = false   # 禁用溢出检查 (性能优化)
debug-assertions = false  # 禁用调试断言
incremental = false       # 禁用增量编译 (更好的优化)

[profile.release-obfuscated]
# 混淆版本配置 (继承 release 配置)
inherits = "release"
opt-level = "z"           # 优化二进制大小
lto = "fat"
codegen-units = 1
panic = "abort"
strip = "symbols"
debug = false
split-debuginfo = "off"
rpath = false

# 环境变量配置
[env]
# 设置 RUSTFLAGS 用于额外的编译器标志
RUSTFLAGS = [
    "-C", "target-cpu=native",
    "-C", "prefer-dynamic=no",
    "-C", "embed-bitcode=yes",
    "-C", "force-frame-pointers=no"
]

# 注册表配置 (使用国内镜像加速)
[source.crates-io]
registry = "https://github.com/rust-lang/crates.io-index"
replace-with = "ustc"

[source.ustc]
registry = "git://mirrors.ustc.edu.cn/crates.io-index"

# 备用镜像
[source.tuna]
registry = "https://mirrors.tuna.tsinghua.edu.cn/git/crates.io-index.git"

[source.sjtu]
registry = "https://mirrors.sjtug.sjtu.edu.cn/git/crates.io-index"
