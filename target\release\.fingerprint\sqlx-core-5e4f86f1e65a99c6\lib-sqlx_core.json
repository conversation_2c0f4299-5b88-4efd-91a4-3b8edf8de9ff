{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 9719146384424564959, "path": 8007707607423674868, "deps": [[5103565458935487, "futures_io", false, 10809827187507672698], [40386456601120721, "percent_encoding", false, 3986832771394037279], [530211389790465181, "hex", false, 1568870286320374834], [788558663644978524, "crossbeam_queue", false, 3294186640284197183], [966925859616469517, "ahash", false, 14595160524223416177], [1162433738665300155, "crc", false, 13910234777806448461], [1464803193346256239, "event_listener", false, 6373634229404168128], [1811549171721445101, "futures_channel", false, 2172206526262036268], [3150220818285335163, "url", false, 12588152225770312754], [3405817021026194662, "hashlink", false, 14776182418155626672], [3646857438214563691, "futures_intrusive", false, 2976539022974130056], [3666196340704888985, "smallvec", false, 14414880233091647919], [3712811570531045576, "byteorder", false, 14179023907723251919], [3722963349756955755, "once_cell", false, 9138643898515270603], [5986029879202738730, "log", false, 3872217859305079192], [6493259146304816786, "indexmap", false, 10876654964101330376], [7620660491849607393, "futures_core", false, 4346228350226202045], [8008191657135824715, "thiserror", false, 4415865075283085700], [8319709847752024821, "uuid", false, 13774689303143432824], [8606274917505247608, "tracing", false, 4803911688178506958], [9689903380558560274, "serde", false, 751882074093274886], [9857275760291862238, "sha2", false, 8445620856012785845], [9897246384292347999, "chrono", false, 8676675372847466725], [10629569228670356391, "futures_util", false, 11345212762805468809], [10862088793507253106, "sqlformat", false, 10768901920033405228], [11295624341523567602, "rustls", false, 7121117722389226100], [12170264697963848012, "either", false, 9544921243633991718], [15932120279885307830, "memchr", false, 3026514914881548915], [16066129441945555748, "bytes", false, 17417613959898761787], [16311359161338405624, "rustls_pemfile", false, 12524359472864638133], [16362055519698394275, "serde_json", false, 16263208147615559115], [16973251432615581304, "tokio_stream", false, 10333213450370632864], [17106256174509013259, "atoi", false, 15980980701964074700], [17531218394775549125, "tokio", false, 438683922267788071], [17605717126308396068, "paste", false, 11364282378793036223], [17652733826348741533, "webpki_roots", false, 17994441500869081413]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-core-5e4f86f1e65a99c6\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}