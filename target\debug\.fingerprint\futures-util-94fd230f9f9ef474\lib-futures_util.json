{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 3337381004082405530, "deps": [[5103565458935487, "futures_io", false, 4063379907736820283], [1615478164327904835, "pin_utils", false, 1411137723164646029], [1906322745568073236, "pin_project_lite", false, 7109322642501522252], [5451793922601807560, "slab", false, 1094061657576814369], [7013762810557009322, "futures_sink", false, 11806518717762718874], [7620660491849607393, "futures_core", false, 17996020517316536911], [10565019901765856648, "futures_macro", false, 15016025012758705166], [15932120279885307830, "memchr", false, 18328080156587498744], [16240732885093539806, "futures_task", false, 7942805824791348492]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-94fd230f9f9ef474\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}