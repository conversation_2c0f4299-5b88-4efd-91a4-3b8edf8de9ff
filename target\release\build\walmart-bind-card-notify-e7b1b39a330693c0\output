cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=src/
cargo:rustc-env=BUILD_TARGET=x86_64-pc-windows-msvc
cargo:rustc-env=BUILD_PROFILE=release
cargo:rustc-env=BUILD_TIMESTAMP=1754407501
cargo:rustc-link-arg=-s
🔧 构建配置:
   目标平台: x86_64-pc-windows-msvc
   构建配置: release
   输出目录: D:\Document\Code\YuXinYang\python\walmart\walmart-bind-card-notify\target\release\build\walmart-bind-card-notify-e7b1b39a330693c0\out
🔒 启用代码混淆和优化
