use walmart_bind_card_notify::{
    init_logging, CallbackConfig, CallbackService, VERSION, NAME
};
use tracing::{info, error};

// 包含构建信息
include!(concat!(env!("OUT_DIR"), "/build_info.rs"));
use tokio::signal;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 检查命令行参数
    let args: Vec<String> = env::args().collect();
    if args.len() > 1 {
        match args[1].as_str() {
            "--version" | "-v" => {
                println!("{}", get_build_info());
                return Ok(());
            }
            "--health-check" => {
                // 简单的健康检查
                println!("OK");
                return Ok(());
            }
            "--help" | "-h" => {
                println!("Walmart 绑卡回调通知服务");
                println!("用法: {} [选项]", args[0]);
                println!("选项:");
                println!("  --version, -v     显示版本信息");
                println!("  --health-check    健康检查");
                println!("  --help, -h        显示帮助信息");
                return Ok(());
            }
            _ => {}
        }
    }

    // 初始化日志
    init_logging();

    info!("🦀 启动 Walmart 绑卡回调通知服务");
    info!("构建信息: {}", get_build_info());

    // 加载配置
    let config = load_config().await?;
    info!("配置加载成功");

    // 创建回调服务
    let callback_service = CallbackService::new(config).await?;
    info!("回调服务创建成功");

    // 启动服务
    callback_service.start().await?;
    info!("回调服务启动成功");

    // 等待关闭信号
    info!("服务运行中，按 Ctrl+C 停止...");
    wait_for_shutdown().await;

    // 优雅关闭
    info!("收到关闭信号，开始优雅关闭...");
    if let Err(e) = callback_service.shutdown().await {
        error!("关闭服务时出错: {}", e);
    }

    info!("服务已关闭");
    Ok(())
}

/// 加载配置
async fn load_config() -> Result<CallbackConfig, Box<dyn std::error::Error>> {
    // 优先从环境变量加载配置
    if let Ok(config) = CallbackConfig::from_env() {
        info!("从环境变量加载配置");
        return Ok(config);
    }

    // 从配置文件加载
    let config_path = env::var("CONFIG_PATH").unwrap_or_else(|_| "config.toml".to_string());

    match CallbackConfig::from_file(&config_path) {
        Ok(config) => {
            info!("从配置文件加载配置: {}", config_path);
            Ok(config)
        }
        Err(_) => {
            info!("使用默认配置");
            Ok(CallbackConfig::default())
        }
    }
}

/// 等待关闭信号
async fn wait_for_shutdown() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }
}