# Walmart绑卡回调通知服务 - 部署指南

## 🚀 快速开始

### 1. 开发环境部署

```bash
# 克隆项目
git clone <repository-url>
cd walmart-bind-card-notify

# 构建项目
./build.sh  # Linux/macOS
# 或
build.bat   # Windows

# 运行服务
cargo run
```

### 2. Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f callback-service
```

## 🏗️ 生产环境部署

### 方式1: 直接部署

#### 系统要求
- Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- 4GB+ RAM
- 2+ CPU cores
- 20GB+ 磁盘空间

#### 安装步骤

1. **安装依赖**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y libssl3 libmysqlclient21

# CentOS/RHEL
sudo yum install -y openssl mysql-libs
```

2. **创建用户和目录**
```bash
sudo useradd -r -s /bin/false walmart
sudo mkdir -p /opt/walmart-callback
sudo mkdir -p /var/log/walmart-callback
sudo chown walmart:walmart /opt/walmart-callback
sudo chown walmart:walmart /var/log/walmart-callback
```

3. **部署应用**
```bash
# 复制二进制文件
sudo cp target/release/walmart-bind-card-notify /opt/walmart-callback/
sudo cp config.toml /opt/walmart-callback/
sudo chown walmart:walmart /opt/walmart-callback/*
sudo chmod +x /opt/walmart-callback/walmart-bind-card-notify
```

4. **配置systemd服务**
```bash
sudo tee /etc/systemd/system/walmart-callback.service > /dev/null <<EOF
[Unit]
Description=Walmart Callback Service
After=network.target mysql.service redis.service

[Service]
Type=simple
User=walmart
Group=walmart
WorkingDirectory=/opt/walmart-callback
ExecStart=/opt/walmart-callback/walmart-bind-card-notify
Environment=CONFIG_PATH=/opt/walmart-callback/config.toml
Environment=RUST_LOG=info
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable walmart-callback
sudo systemctl start walmart-callback
```

### 方式2: Docker部署

#### 生产环境docker-compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  callback-service:
    image: walmart-callback:latest
    restart: unless-stopped
    environment:
      - RUST_LOG=warn
      - WALMART_CALLBACK_DATABASE_URL=${DATABASE_URL}
      - WALMART_CALLBACK_REDIS_URL=${REDIS_URL}
      - WALMART_CALLBACK_RABBITMQ_URL=${RABBITMQ_URL}
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    networks:
      - walmart-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

#### 部署命令

```bash
# 构建镜像
docker build -t walmart-callback:latest .

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 扩容服务
docker-compose -f docker-compose.prod.yml up -d --scale callback-service=5
```

### 方式3: Kubernetes部署

#### 创建配置文件

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: walmart-callback
  labels:
    app: walmart-callback
spec:
  replicas: 3
  selector:
    matchLabels:
      app: walmart-callback
  template:
    metadata:
      labels:
        app: walmart-callback
    spec:
      containers:
      - name: callback-service
        image: walmart-callback:latest
        ports:
        - containerPort: 8080
        env:
        - name: RUST_LOG
          value: "info"
        - name: WALMART_CALLBACK_DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: walmart-secrets
              key: database-url
        resources:
          limits:
            memory: "512Mi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "250m"
        livenessProbe:
          exec:
            command:
            - /opt/walmart-callback/walmart-bind-card-notify
            - --health-check
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - /opt/walmart-callback/walmart-bind-card-notify
            - --health-check
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### 部署到Kubernetes

```bash
# 创建命名空间
kubectl create namespace walmart

# 创建密钥
kubectl create secret generic walmart-secrets \
  --from-literal=database-url="mysql://..." \
  --from-literal=redis-url="redis://..." \
  --from-literal=rabbitmq-url="amqp://..." \
  -n walmart

# 部署应用
kubectl apply -f k8s/ -n walmart

# 查看状态
kubectl get pods -n walmart
kubectl logs -f deployment/walmart-callback -n walmart
```

## 🔧 配置管理

### 环境变量配置

```bash
# 数据库配置
export WALMART_CALLBACK_DATABASE_URL="mysql://user:pass@host:3306/db"
export WALMART_CALLBACK_DATABASE_MAX_CONNECTIONS=20

# Redis配置
export WALMART_CALLBACK_REDIS_URL="redis://host:6379"
export WALMART_CALLBACK_REDIS_MAX_CONNECTIONS=10

# RabbitMQ配置
export WALMART_CALLBACK_RABBITMQ_URL="amqp://user:pass@host:5672/%2f"
export WALMART_CALLBACK_RABBITMQ_QUEUE_NAME="callback_queue"

# 回调配置
export WALMART_CALLBACK_CALLBACK_MAX_RETRIES=3
export WALMART_CALLBACK_CALLBACK_TIMEOUT_SECONDS=10
export WALMART_CALLBACK_CALLBACK_MAX_CONCURRENT=100

# 日志配置
export RUST_LOG=info
export RUST_BACKTRACE=1
```

### 配置文件模板

```toml
# config.prod.toml
[database]
url = "${DATABASE_URL}"
max_connections = 20
min_connections = 5
connect_timeout = 30
idle_timeout = 600

[redis]
url = "${REDIS_URL}"
max_connections = 20
connect_timeout = 5
command_timeout = 5

[rabbitmq]
url = "${RABBITMQ_URL}"
queue_name = "bind_card_callback_queue"
exchange_name = "walmart_exchange"
routing_key = "callback"
prefetch_count = 1
max_retries = 3

[callback]
max_retries = 5
timeout_seconds = 15
retry_delay_seconds = 2
max_retry_delay_seconds = 60
max_concurrent = 200
dedup_ttl_seconds = 600  # 10分钟

[logging]
level = "warn"
format = "json"
file_enabled = true
file_path = "/var/log/walmart-callback/app.log"
```

## 📊 监控和运维

### 健康检查

```bash
# 检查服务状态
systemctl status walmart-callback

# 检查服务健康
curl -f http://localhost:8080/health || echo "Service unhealthy"

# 查看日志
journalctl -u walmart-callback -f
```

### 性能监控

```bash
# 查看资源使用
top -p $(pgrep walmart-bind-card-notify)

# 查看网络连接
netstat -tulpn | grep walmart-bind-card-notify

# 查看文件描述符
lsof -p $(pgrep walmart-bind-card-notify)
```

### 日志管理

```bash
# 配置logrotate
sudo tee /etc/logrotate.d/walmart-callback > /dev/null <<EOF
/var/log/walmart-callback/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 walmart walmart
    postrotate
        systemctl reload walmart-callback
    endscript
}
EOF
```

## 🔒 安全配置

### 防火墙设置

```bash
# 只允许必要的端口
sudo ufw allow from 10.0.0.0/8 to any port 3306  # MySQL
sudo ufw allow from 10.0.0.0/8 to any port 6379  # Redis
sudo ufw allow from 10.0.0.0/8 to any port 5672  # RabbitMQ
```

### SSL/TLS配置

```toml
# 在config.toml中启用TLS
[database]
url = "mysql://user:pass@host:3306/db?ssl-mode=required"

[redis]
url = "rediss://host:6380"  # 使用SSL

[rabbitmq]
url = "amqps://user:pass@host:5671/%2f"  # 使用SSL
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
```bash
# 检查配置文件
/opt/walmart-callback/walmart-bind-card-notify --check-config

# 检查依赖服务
systemctl status mysql redis rabbitmq-server
```

2. **内存使用过高**
```bash
# 调整配置
# 减少max_connections和max_concurrent
# 增加系统内存或使用swap
```

3. **连接超时**
```bash
# 检查网络连接
telnet mysql-host 3306
telnet redis-host 6379
telnet rabbitmq-host 5672
```

### 紧急恢复

```bash
# 停止服务
sudo systemctl stop walmart-callback

# 备份数据
mysqldump -u root -p walmart_card_db > backup.sql

# 重启依赖服务
sudo systemctl restart mysql redis rabbitmq-server

# 重启应用服务
sudo systemctl start walmart-callback
```
