{"rustc": 1842507548689473721, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 3592778941406178886, "path": 11915375748565866290, "deps": [[40386456601120721, "percent_encoding", false, 15461567079426116486], [95042085696191081, "ipnet", false, 8173364100894012924], [264090853244900308, "sync_wrapper", false, 13064011469339271385], [784494742817713399, "tower_service", false, 17975638539159341236], [1906322745568073236, "pin_project_lite", false, 11022751310630360985], [3150220818285335163, "url", false, 3188455783886142279], [3722963349756955755, "once_cell", false, 4623307841458457968], [4405182208873388884, "http", false, 5798332029874251625], [5986029879202738730, "log", false, 3143729194340817726], [7414427314941361239, "hyper", false, 3027323864279630247], [7620660491849607393, "futures_core", false, 7117656570195926816], [8405603588346937335, "winreg", false, 10737722972130687601], [8915503303801890683, "http_body", false, 1515521096803035640], [9689903380558560274, "serde", false, 5616481245319993094], [10229185211513642314, "mime", false, 40801610804656906], [10629569228670356391, "futures_util", false, 14323013482787026363], [12186126227181294540, "tokio_native_tls", false, 9777813111558130230], [12367227501898450486, "hyper_tls", false, 2924048480819700284], [13763625454224483636, "h2", false, 10155618544651764551], [14564311161534545801, "encoding_rs", false, 1137997296053839039], [16066129441945555748, "bytes", false, 9094909366198335868], [16311359161338405624, "rustls_pemfile", false, 4444031905493527596], [16362055519698394275, "serde_json", false, 17317803994796681169], [16542808166767769916, "serde_urlencoded", false, 15748779933960200827], [16785601910559813697, "native_tls_crate", false, 9602138688668325288], [17531218394775549125, "tokio", false, 2041435398497995185], [18066890886671768183, "base64", false, 11706976288958648656]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-2885efdab4f471df\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}