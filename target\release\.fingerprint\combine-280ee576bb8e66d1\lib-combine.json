{"rustc": 1842507548689473721, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 3592778941406178886, "path": 300145601762188890, "deps": [[1906322745568073236, "pin_project_lite", false, 11022751310630360985], [7620660491849607393, "futures_core_03", false, 7117656570195926816], [15894030960229394068, "tokio_util", false, 7052716708192602676], [15932120279885307830, "memchr", false, 5905481241054209797], [16066129441945555748, "bytes", false, 9094909366198335868], [17531218394775549125, "tokio_dep", false, 2041435398497995185]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\combine-280ee576bb8e66d1\\dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}