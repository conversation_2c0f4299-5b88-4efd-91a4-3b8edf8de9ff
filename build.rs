// 构建脚本 - 用于代码混淆和优化

use std::env;
use std::fs;
use std::path::Path;

fn main() {
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=src/");
    
    // 获取构建信息
    let target = env::var("TARGET").unwrap_or_default();
    let profile = env::var("PROFILE").unwrap_or_default();
    let out_dir = env::var("OUT_DIR").unwrap_or_default();
    
    println!("cargo:rustc-env=BUILD_TARGET={}", target);
    println!("cargo:rustc-env=BUILD_PROFILE={}", profile);
    println!("cargo:rustc-env=BUILD_TIMESTAMP={}", chrono::Utc::now().timestamp());
    
    // 设置版本信息
    if let Ok(git_hash) = std::process::Command::new("git")
        .args(&["rev-parse", "--short", "HEAD"])
        .output()
    {
        if git_hash.status.success() {
            let hash = String::from_utf8_lossy(&git_hash.stdout).trim().to_string();
            println!("cargo:rustc-env=GIT_HASH={}", hash);
        }
    }
    
    // 混淆配置
    if profile == "release" || profile.contains("obfuscated") {
        println!("cargo:rustc-link-arg=-s");  // 剥离符号
        
        // 如果是 Linux 目标，添加额外的链接参数
        if target.contains("linux") {
            println!("cargo:rustc-link-arg=-Wl,--gc-sections");  // 垃圾回收未使用的段
            println!("cargo:rustc-link-arg=-Wl,--strip-all");    // 剥离所有符号
        }
    }
    
    // 生成构建信息文件
    let build_info = format!(
        r#"
// 自动生成的构建信息 - 请勿手动修改

pub const BUILD_TARGET: &str = "{}";
pub const BUILD_PROFILE: &str = "{}";
pub const BUILD_TIMESTAMP: i64 = {};
pub const GIT_HASH: &str = "{}";
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");

pub fn get_build_info() -> String {{
    format!(
        "{} v{} ({}) built for {} at {}",
        NAME,
        VERSION,
        GIT_HASH,
        BUILD_TARGET,
        BUILD_TIMESTAMP
    )
}}
"#,
        target,
        profile,
        chrono::Utc::now().timestamp(),
        env::var("GIT_HASH").unwrap_or_else(|_| "unknown".to_string())
    );
    
    let build_info_path = Path::new(&out_dir).join("build_info.rs");
    fs::write(build_info_path, build_info).expect("无法写入构建信息文件");
    
    println!("🔧 构建配置:");
    println!("   目标平台: {}", target);
    println!("   构建配置: {}", profile);
    println!("   输出目录: {}", out_dir);
    
    // 检查是否为交叉编译
    if target != env::var("HOST").unwrap_or_default() {
        println!("🎯 交叉编译模式: {} -> {}", 
                 env::var("HOST").unwrap_or_default(), 
                 target);
    }
    
    // 混淆提示
    if profile.contains("obfuscated") || profile == "release" {
        println!("🔒 启用代码混淆和优化");
    }
}
