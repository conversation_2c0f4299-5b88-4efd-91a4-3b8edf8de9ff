// 核心回调服务模块
use crate::config::CallbackConfig;
use crate::database::DatabaseManager;
use crate::errors::{CallbackError, CallbackResult};
use crate::http::{CallbackHttpClient, CallbackResponse};
use crate::models::{CallbackMessage, CallbackPayload, CallbackStatus};
use crate::queue::{QueueManager, MessagePublisher};
use crate::utils::{DeduplicationService, helpers};
use std::sync::Arc;
use tokio::sync::Semaphore;
use tracing::{info, warn, error, debug};

/// 主要的回调服务
#[derive(Debug)]
pub struct CallbackService {
    config: CallbackConfig,
    database: Arc<DatabaseManager>,
    http_client: Arc<CallbackHttpClient>,
    dedup_service: Arc<DeduplicationService>,
    queue_manager: Arc<QueueManager>,
    message_publisher: MessagePublisher,
    // 并发控制
    semaphore: Arc<Semaphore>,
}

impl CallbackService {
    pub async fn new(config: CallbackConfig) -> CallbackResult<Self> {
        info!("初始化回调服务...");
        
        // 初始化数据库
        let database = Arc::new(DatabaseManager::new(&config.database).await?);
        
        // 初始化HTTP客户端
        let http_client = Arc::new(CallbackHttpClient::new(&config.callback)?);
        
        // 初始化去重服务
        let dedup_service = Arc::new(
            DeduplicationService::new(&config.redis, config.callback.dedup_ttl()).await?
        );
        
        // 初始化消息队列
        let queue_manager = Arc::new(QueueManager::new(config.rabbitmq.clone()).await?);
        let message_publisher = MessagePublisher::new(Arc::clone(&queue_manager));
        
        // 并发控制
        let semaphore = Arc::new(Semaphore::new(config.callback.maximum_concurrent_requests));
        
        info!("回调服务初始化成功");
        
        Ok(Self {
            config,
            database,
            http_client,
            dedup_service,
            queue_manager,
            message_publisher,
            semaphore,
        })
    }
    
    /// 启动服务
    pub async fn start(&self) -> CallbackResult<()> {
        info!("启动回调服务...");
        
        // 启动消息消费者
        let service_clone = self.clone_for_handler();
        self.queue_manager.start_consumer(move |message| {
            let service = service_clone.clone();
            async move {
                service.handle_callback_message(message).await
            }
        }).await?;
        
        info!("回调服务启动成功");
        Ok(())
    }
    
    /// 处理回调消息的核心逻辑
    async fn handle_callback_message(&self, message: CallbackMessage) -> CallbackResult<()> {
        let record_id = &message.record_identifier;
        let retry_count = message.retry_attempt;
        
        info!("开始处理回调消息: record_id={}, retry_count={}", record_id, retry_count);
        
        // 获取并发控制许可
        let _permit = self.semaphore.acquire().await.map_err(|_| {
            CallbackError::unknown("获取并发许可失败")
        })?;
        
        // 去重检查 - 防止重复处理
        if self.dedup_service.is_processing(record_id, retry_count).await? {
            info!("消息正在被其他进程处理，跳过: record_id={}, retry_count={}", record_id, retry_count);
            return Ok(());
        }
        
        // 获取处理锁
        let _processing_guard = self.dedup_service
            .acquire_processing_lock(record_id, retry_count)
            .await?;
        
        // 检查回调是否已成功
        if self.database.is_callback_successful(record_id).await? {
            info!("回调已成功完成，跳过处理: record_id={}", record_id);
            return Ok(());
        }
        
        // 检查重试次数
        if retry_count >= self.config.callback.maximum_retry_attempts {
            warn!("回调重试次数已达上限，停止重试: record_id={}, max_retries={}", 
                  record_id, self.config.callback.maximum_retry_attempts);
            
            // 更新状态为最大重试次数已达
            self.database.update_callback_status(
                record_id, 
                CallbackStatus::MaxRetriesReached
            ).await?;
            
            return Ok(());
        }
        
        // 执行回调
        match self.execute_callback(record_id, &message).await {
            Ok(success) => {
                if success {
                    // 回调成功
                    self.database.update_callback_status(
                        record_id, 
                        CallbackStatus::Success
                    ).await?;
                    
                    info!("回调处理成功: record_id={}, retry_count={}", record_id, retry_count);
                } else {
                    // 回调失败，安排重试
                    self.schedule_retry(&message).await?;
                }
            }
            Err(e) => {
                error!("回调处理异常: record_id={}, error={}", record_id, e);
                
                if e.is_retryable() {
                    // 可重试错误，安排重试
                    self.schedule_retry(&message).await?;
                } else {
                    // 不可重试错误，标记为失败
                    self.database.update_callback_status(
                        record_id, 
                        CallbackStatus::Failed
                    ).await?;
                }
            }
        }
        
        Ok(())
    }
    
    /// 执行回调请求
    async fn execute_callback(
        &self, 
        record_id: &str, 
        message: &CallbackMessage
    ) -> CallbackResult<bool> {
        // 获取卡记录
        let card_record = self.database.get_card_record(record_id).await?
            .ok_or_else(|| CallbackError::business(format!("卡记录不存在: {}", record_id)))?;
        
        // 获取商户信息
        let merchant = self.database.get_merchant(card_record.merchant_identifier).await?
            .ok_or_else(|| CallbackError::business(format!("商户不存在: {}", card_record.merchant_identifier)))?;
        
        // 检查商户是否配置了回调URL
        let callback_url = merchant.callback_endpoint
            .ok_or_else(|| CallbackError::business(format!("商户未配置回调URL: {}", merchant.merchant_id)))?;
        
        // 验证URL格式
        if !helpers::is_valid_url(&callback_url) {
            return Err(CallbackError::business(format!("无效的回调URL: {}", callback_url)));
        }
        
        // 构建回调负载
        let mut payload = CallbackPayload::from(card_record);
        payload.retry_count = message.retry_attempt;
        
        // 如果是失败状态，添加失败原因
        if payload.status == "failed" {
            // 这里可以从数据库或其他地方获取失败原因
            payload.fail_reason = Some("绑卡处理失败".to_string());
        }
        
        // 通知去重检查
        let error_hash = helpers::hash_error_message(&payload.status);
        if self.dedup_service.is_notification_sent(record_id, &error_hash).await? {
            info!("相同通知已发送，跳过: record_id={}, status={}", record_id, payload.status);
            return Ok(true); // 视为成功，避免重复通知
        }
        
        // 发送回调请求
        debug!("发送回调请求: record_id={}, url={}", record_id, callback_url);
        let response = self.http_client.send_callback(&callback_url, &payload, record_id).await?;
        
        if response.is_success() {
            // 标记通知已发送
            self.dedup_service.mark_notification_sent(record_id, &error_hash).await?;
            Ok(true)
        } else {
            warn!("回调请求失败: record_id={}, status_code={}, should_retry={}", 
                  record_id, response.status_code, response.should_retry());
            Ok(false)
        }
    }
    
    /// 安排重试
    async fn schedule_retry(&self, message: &CallbackMessage) -> CallbackResult<()> {
        let next_retry = message.retry_attempt + 1;
        
        if next_retry >= self.config.callback.maximum_retry_attempts {
            warn!("已达最大重试次数，不再重试: record_id={}", message.record_identifier);
            return Ok(());
        }
        
        // 计算退避延迟
        let delay = helpers::calculate_backoff_delay(
            next_retry,
            self.config.callback.retry_delay(),
            self.config.callback.max_retry_delay(),
        );
        
        // 创建重试消息
        let retry_message = CallbackMessage {
            record_identifier: message.record_identifier.clone(),
            merchant_identifier: message.merchant_identifier,
            retry_attempt: next_retry,
            extension_data: message.extension_data.clone(),
            trace_identifier: message.trace_identifier.clone(),
        };
        
        // 发送延迟消息
        self.message_publisher.send_delayed_callback_message(
            retry_message, 
            delay.as_secs()
        ).await?;
        
        info!("安排回调重试: record_id={}, retry_count={}, delay={:?}", 
              message.record_identifier, next_retry, delay);
        
        Ok(())
    }
    
    /// 健康检查
    pub async fn health_check(&self) -> CallbackResult<HealthStatus> {
        let database_ok = self.database.health_check().await?;
        let redis_ok = self.dedup_service.health_check().await?;
        let queue_ok = self.queue_manager.health_check().await;
        
        Ok(HealthStatus {
            database: database_ok,
            redis: redis_ok,
            queue: queue_ok,
            overall: database_ok && redis_ok && queue_ok,
        })
    }
    
    /// 获取服务统计信息
    pub async fn get_stats(&self) -> CallbackResult<ServiceStats> {
        let queue_status = self.queue_manager.get_queue_status().await?;
        let (pool_size, pool_idle) = self.database.pool_status();
        
        Ok(ServiceStats {
            queue_message_count: queue_status.message_count,
            queue_consumer_count: queue_status.consumer_count,
            database_pool_size: pool_size,
            database_pool_idle: pool_idle,
            concurrent_permits_available: self.semaphore.available_permits(),
        })
    }
    
    /// 关闭服务
    pub async fn shutdown(&self) -> CallbackResult<()> {
        info!("关闭回调服务...");
        
        // 关闭队列连接
        self.queue_manager.close().await?;
        
        // 关闭数据库连接
        self.database.close().await;
        
        info!("回调服务已关闭");
        Ok(())
    }
    
    /// 为消息处理器克隆必要的组件
    fn clone_for_handler(&self) -> CallbackServiceHandler {
        CallbackServiceHandler {
            config: self.config.clone(),
            database: Arc::clone(&self.database),
            http_client: Arc::clone(&self.http_client),
            dedup_service: Arc::clone(&self.dedup_service),
            message_publisher: self.message_publisher.clone(),
            semaphore: Arc::clone(&self.semaphore),
        }
    }
}

/// 用于消息处理的轻量级服务处理器
#[derive(Debug, Clone)]
struct CallbackServiceHandler {
    config: CallbackConfig,
    database: Arc<DatabaseManager>,
    http_client: Arc<CallbackHttpClient>,
    dedup_service: Arc<DeduplicationService>,
    message_publisher: MessagePublisher,
    semaphore: Arc<Semaphore>,
}

impl CallbackServiceHandler {
    async fn handle_callback_message(&self, message: CallbackMessage) -> CallbackResult<()> {
        let record_id = &message.record_identifier;
        let retry_count = message.retry_attempt;

        info!("开始处理回调消息: record_id={}, retry_count={}", record_id, retry_count);

        // 获取并发控制许可
        let _permit = self.semaphore.acquire().await.map_err(|_| {
            CallbackError::unknown("获取并发许可失败")
        })?;

        // 去重检查 - 防止重复处理
        if self.dedup_service.is_processing(record_id, retry_count).await? {
            info!("消息正在被其他进程处理，跳过: record_id={}, retry_count={}", record_id, retry_count);
            return Ok(());
        }

        // 获取处理锁
        let _processing_guard = self.dedup_service
            .acquire_processing_lock(record_id, retry_count)
            .await?;

        // 检查回调是否已成功
        if self.database.is_callback_successful(record_id).await? {
            info!("回调已成功完成，跳过处理: record_id={}", record_id);
            return Ok(());
        }

        // 检查重试次数
        if retry_count >= self.config.callback.maximum_retry_attempts {
            warn!("回调重试次数已达上限，停止重试: record_id={}, max_retries={}",
                  record_id, self.config.callback.maximum_retry_attempts);

            // 更新状态为最大重试次数已达
            self.database.update_callback_status(
                record_id,
                CallbackStatus::MaxRetriesReached
            ).await?;

            return Ok(());
        }

        info!("回调消息处理完成: record_id={}, retry_count={}", record_id, retry_count);
        Ok(())
    }
}

/// 健康状态
#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub database: bool,
    pub redis: bool,
    pub queue: bool,
    pub overall: bool,
}

/// 服务统计信息
#[derive(Debug, Clone)]
pub struct ServiceStats {
    pub queue_message_count: u32,
    pub queue_consumer_count: u32,
    pub database_pool_size: u32,
    pub database_pool_idle: u32,
    pub concurrent_permits_available: usize,
}
