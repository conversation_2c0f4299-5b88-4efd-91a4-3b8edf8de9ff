// 消息队列模块 - RabbitMQ集成
use crate::config::RabbitMQConfig;
use crate::errors::{CallbackResult};
use crate::models::CallbackMessage;
use lapin::{
    options::*, types::FieldTable, BasicProperties, Channel, Connection,
    ConnectionProperties, ExchangeKind,
};
use futures_util::stream::StreamExt;
use serde_json;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{info, warn, error, debug};

/// 消息队列管理器
#[derive(Debug)]
pub struct QueueManager {
    connection: Connection,
    channel: Arc<Mutex<Channel>>,
    config: RabbitMQConfig,
}

impl QueueManager {
    pub async fn new(config: RabbitMQConfig) -> CallbackResult<Self> {
        info!("初始化RabbitMQ连接...");
        
        let connection = Connection::connect(
            &config.connection_url,
            ConnectionProperties::default(),
        ).await?;
        
        let channel = connection.create_channel().await?;
        
        // 设置QoS - 每次只处理一条消息
        channel.basic_qos(config.consumer_prefetch_count, BasicQosOptions::default()).await?;
        
        // 声明交换机
        channel.exchange_declare(
            &config.exchange_name,
            ExchangeKind::Direct,
            ExchangeDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        ).await?;
        
        // 声明队列
        channel.queue_declare(
            &config.callback_queue_name,
            QueueDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        ).await?;
        
        // 绑定队列到交换机
        channel.queue_bind(
            &config.callback_queue_name,
            &config.exchange_name,
            &config.routing_key,
            QueueBindOptions::default(),
            FieldTable::default(),
        ).await?;
        
        info!("RabbitMQ连接初始化成功");
        
        Ok(Self {
            connection,
            channel: Arc::new(Mutex::new(channel)),
            config,
        })
    }
    
    /// 启动消费者
    pub async fn start_consumer<F, Fut>(&self, handler: F) -> CallbackResult<()>
    where
        F: Fn(CallbackMessage) -> Fut + Send + Sync + 'static,
        Fut: std::future::Future<Output = CallbackResult<()>> + Send + 'static,
    {
        info!("启动回调消息消费者...");
        
        let channel = self.channel.lock().await;
        let consumer = channel.basic_consume(
            &self.config.callback_queue_name,
            "callback_consumer",
            BasicConsumeOptions {
                no_ack: false, // 手动确认消息
                ..Default::default()
            },
            FieldTable::default(),
        ).await?;
        
        drop(channel); // 释放锁
        
        let handler = Arc::new(handler);
        let channel_clone = Arc::clone(&self.channel);
        
        tokio::spawn(async move {
            let mut consumer_stream = consumer;

            loop {
                match consumer_stream.next().await {
                    Some(delivery_result) => {
                        let delivery = match delivery_result {
                            Ok(delivery) => delivery,
                            Err(e) => {
                                error!("接收消息时发生错误: {}", e);
                                continue;
                            }
                        };
                        let message_data = String::from_utf8_lossy(&delivery.data);
                        debug!("收到回调消息: {}", message_data);
                        
                        // 解析消息
                        match serde_json::from_slice::<CallbackMessage>(&delivery.data) {
                            Ok(callback_msg) => {
                                let handler_clone = Arc::clone(&handler);
                                let channel_clone = Arc::clone(&channel_clone);
                                let delivery_tag = delivery.delivery_tag;
                                
                                // 处理消息
                                tokio::spawn(async move {
                                    let result = handler_clone(callback_msg).await;
                                    let channel = channel_clone.lock().await;
                                    
                                    match result {
                                        Ok(_) => {
                                            // 处理成功，确认消息
                                            if let Err(e) = channel.basic_ack(delivery_tag, BasicAckOptions::default()).await {
                                                error!("确认消息失败: {}", e);
                                            } else {
                                                debug!("消息处理成功并已确认: tag={}", delivery_tag);
                                            }
                                        }
                                        Err(e) => {
                                            // 处理失败，根据错误类型决定是否重试
                                            if e.is_retryable() {
                                                warn!("消息处理失败，将重新入队: {}", e);
                                                if let Err(nack_err) = channel.basic_nack(
                                                    delivery_tag, 
                                                    BasicNackOptions { 
                                                        requeue: true, 
                                                        multiple: false 
                                                    }
                                                ).await {
                                                    error!("拒绝消息失败: {}", nack_err);
                                                }
                                            } else {
                                                error!("消息处理失败，丢弃消息: {}", e);
                                                if let Err(ack_err) = channel.basic_ack(delivery_tag, BasicAckOptions::default()).await {
                                                    error!("确认失败消息失败: {}", ack_err);
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                            Err(e) => {
                                error!("解析回调消息失败: {}, 原始数据: {}", e, message_data);
                                // 解析失败的消息直接确认（丢弃）
                                let channel = channel_clone.lock().await;
                                if let Err(ack_err) = channel.basic_ack(delivery.delivery_tag, BasicAckOptions::default()).await {
                                    error!("确认解析失败消息失败: {}", ack_err);
                                }
                            }
                        }
                    }
                    Ok(None) => {
                        // 消费者已关闭
                        break;
                    }
                    Err(e) => {
                        error!("接收消息失败: {}", e);
                        // 短暂延迟后继续
                        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
                    }
                }
            }
        });
        
        info!("回调消息消费者启动成功");
        Ok(())
    }
    
    /// 发布延迟消息（用于重试）
    pub async fn publish_delayed_message(
        &self,
        message: &CallbackMessage,
        delay_seconds: u64,
    ) -> CallbackResult<()> {
        let channel = self.channel.lock().await;
        
        let payload = serde_json::to_vec(message)?;
        
        // 创建延迟队列的属性
        let mut headers = FieldTable::default();
        headers.insert("x-delay".into(), (delay_seconds * 1000).into()); // 毫秒
        
        let properties = BasicProperties::default()
            .with_headers(headers)
            .with_delivery_mode(2); // 持久化消息
        
        channel.basic_publish(
            &self.config.exchange_name,
            &self.config.routing_key,
            BasicPublishOptions::default(),
            &payload,
            properties,
        ).await?;
        
        info!(
            "发布延迟回调消息: record_id={}, delay={}s", 
            message.record_identifier, delay_seconds
        );
        
        Ok(())
    }
    
    /// 发布立即消息
    pub async fn publish_message(&self, message: &CallbackMessage) -> CallbackResult<()> {
        let channel = self.channel.lock().await;
        
        let payload = serde_json::to_vec(message)?;
        
        let properties = BasicProperties::default()
            .with_delivery_mode(2); // 持久化消息
        
        channel.basic_publish(
            &self.config.exchange_name,
            &self.config.routing_key,
            BasicPublishOptions::default(),
            &payload,
            properties,
        ).await?;
        
        debug!("发布回调消息: record_id={}", message.record_identifier);
        
        Ok(())
    }
    
    /// 获取队列状态
    pub async fn get_queue_status(&self) -> CallbackResult<QueueStatus> {
        let channel = self.channel.lock().await;
        
        let queue_info = channel.queue_declare(
            &self.config.callback_queue_name,
            QueueDeclareOptions {
                passive: true, // 只查询，不创建
                ..Default::default()
            },
            FieldTable::default(),
        ).await?;
        
        Ok(QueueStatus {
            queue_name: self.config.callback_queue_name.clone(),
            message_count: queue_info.message_count(),
            consumer_count: queue_info.consumer_count(),
        })
    }
    
    /// 健康检查
    pub async fn health_check(&self) -> bool {
        self.connection.status().connected()
    }
    
    /// 关闭连接
    pub async fn close(&self) -> CallbackResult<()> {
        info!("关闭RabbitMQ连接...");
        
        if let Err(e) = self.connection.close(200, "Normal shutdown").await {
            warn!("关闭RabbitMQ连接时出现警告: {}", e);
        }
        
        info!("RabbitMQ连接已关闭");
        Ok(())
    }
}

/// 队列状态信息
#[derive(Debug, Clone)]
pub struct QueueStatus {
    pub queue_name: String,
    pub message_count: u32,
    pub consumer_count: u32,
}

impl QueueStatus {
    pub fn has_messages(&self) -> bool {
        self.message_count > 0
    }
    
    pub fn has_consumers(&self) -> bool {
        self.consumer_count > 0
    }
}

/// 消息发布器（用于其他服务发送消息到队列）
#[derive(Debug, Clone)]
pub struct MessagePublisher {
    queue_manager: Arc<QueueManager>,
}

impl MessagePublisher {
    pub fn new(queue_manager: Arc<QueueManager>) -> Self {
        Self { queue_manager }
    }
    
    pub async fn send_callback_message(&self, message: CallbackMessage) -> CallbackResult<()> {
        self.queue_manager.publish_message(&message).await
    }
    
    pub async fn send_delayed_callback_message(
        &self, 
        message: CallbackMessage, 
        delay_seconds: u64
    ) -> CallbackResult<()> {
        self.queue_manager.publish_delayed_message(&message, delay_seconds).await
    }
}
