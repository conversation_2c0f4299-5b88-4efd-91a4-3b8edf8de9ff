{"rustc": 1842507548689473721, "features": "[\"alloc\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 3592778941406178886, "path": 14650625315839564467, "deps": [[1573238666360410412, "rand_chacha", false, 17881627705240506321], [18130209639506977569, "rand_core", false, 1775020887263453702]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rand-1c1a6bae06daa555\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}