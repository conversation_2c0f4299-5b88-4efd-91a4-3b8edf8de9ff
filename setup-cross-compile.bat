@echo off
REM 设置 Linux 交叉编译环境 (Windows -> Ubuntu 24)

echo 🎯 设置 Linux 交叉编译环境
echo 目标: Ubuntu 24.04 (x86_64-unknown-linux-gnu)
echo.

REM 检查 Rust 环境
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到 Cargo，请先运行 install-tools.bat
    pause
    exit /b 1
)

echo ✅ Rust 环境检查通过

REM 添加交叉编译目标
echo 📦 添加交叉编译目标...
rustup target add x86_64-unknown-linux-gnu
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 添加交叉编译目标失败
    pause
    exit /b 1
)

echo ✅ 交叉编译目标添加成功

REM 检查是否有 WSL (Windows Subsystem for Linux)
echo 🐧 检查 WSL 环境...
wsl --list --quiet >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 检测到 WSL 环境
    echo 💡 可以使用 WSL 中的 GCC 作为链接器
    
    REM 检查 WSL 中是否有 GCC
    wsl which gcc >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ WSL 中找到 GCC
        echo 💡 建议在 .cargo/config.toml 中配置:
        echo    [target.x86_64-unknown-linux-gnu]
        echo    linker = "wsl gcc"
    ) else (
        echo ⚠️  WSL 中未找到 GCC
        echo 💡 在 WSL 中运行: sudo apt install build-essential
    )
) else (
    echo ⚠️  未检测到 WSL 环境
    echo 💡 建议安装 WSL 以获得更好的交叉编译支持
    echo    在管理员 PowerShell 中运行: wsl --install
)

REM 检查是否安装了 MSYS2/MinGW
echo 🔧 检查 MinGW 环境...
where x86_64-w64-mingw32-gcc >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 找到 MinGW GCC
) else (
    echo ⚠️  未找到 MinGW GCC
    echo 💡 可以通过以下方式安装:
    echo    1. 安装 MSYS2: https://www.msys2.org/
    echo    2. 或使用 Chocolatey: choco install mingw
)

REM 创建或更新 .cargo/config.toml
echo 📝 配置交叉编译设置...

if not exist ".cargo" mkdir .cargo

REM 备份现有配置
if exist ".cargo\config.toml" (
    copy ".cargo\config.toml" ".cargo\config.toml.backup" >nul
    echo ✅ 已备份现有配置到 .cargo\config.toml.backup
)

REM 创建新的配置文件
echo # Cargo 交叉编译配置 - 自动生成 > .cargo\config.toml
echo # 生成时间: %date% %time% >> .cargo\config.toml
echo. >> .cargo\config.toml
echo [build] >> .cargo\config.toml
echo # 默认目标为 Ubuntu 24 >> .cargo\config.toml
echo target = "x86_64-unknown-linux-gnu" >> .cargo\config.toml
echo. >> .cargo\config.toml
echo [target.x86_64-unknown-linux-gnu] >> .cargo\config.toml
echo # 交叉编译链接器配置 >> .cargo\config.toml
echo # 如果有 WSL，取消下面一行的注释: >> .cargo\config.toml
echo # linker = "wsl gcc" >> .cargo\config.toml
echo # 如果有 MinGW，取消下面一行的注释: >> .cargo\config.toml
echo # linker = "x86_64-w64-mingw32-gcc" >> .cargo\config.toml
echo. >> .cargo\config.toml
echo [profile.release] >> .cargo\config.toml
echo # 发布版本优化配置 >> .cargo\config.toml
echo opt-level = 3 >> .cargo\config.toml
echo lto = "fat" >> .cargo\config.toml
echo codegen-units = 1 >> .cargo\config.toml
echo panic = "abort" >> .cargo\config.toml
echo strip = "symbols" >> .cargo\config.toml
echo overflow-checks = false >> .cargo\config.toml
echo debug-assertions = false >> .cargo\config.toml
echo incremental = false >> .cargo\config.toml
echo. >> .cargo\config.toml
echo [profile.release-obfuscated] >> .cargo\config.toml
echo # 混淆版本配置 >> .cargo\config.toml
echo inherits = "release" >> .cargo\config.toml
echo opt-level = "z" >> .cargo\config.toml
echo lto = "fat" >> .cargo\config.toml
echo codegen-units = 1 >> .cargo\config.toml
echo panic = "abort" >> .cargo\config.toml
echo strip = "symbols" >> .cargo\config.toml
echo debug = false >> .cargo\config.toml
echo split-debuginfo = "off" >> .cargo\config.toml
echo rpath = false >> .cargo\config.toml
echo. >> .cargo\config.toml
echo # 中国镜像源配置 >> .cargo\config.toml
echo [source.crates-io] >> .cargo\config.toml
echo registry = "https://github.com/rust-lang/crates.io-index" >> .cargo\config.toml
echo replace-with = "ustc" >> .cargo\config.toml
echo. >> .cargo\config.toml
echo [source.ustc] >> .cargo\config.toml
echo registry = "git://mirrors.ustc.edu.cn/crates.io-index" >> .cargo\config.toml

echo ✅ 交叉编译配置完成

REM 测试交叉编译
echo 🧪 测试交叉编译...
echo 创建测试项目...

if not exist "test-cross-compile" (
    cargo new test-cross-compile --bin --quiet
)

cd test-cross-compile

echo 编译测试...
cargo build --target x86_64-unknown-linux-gnu --quiet
if %ERRORLEVEL% EQU 0 (
    echo ✅ 交叉编译测试成功
    
    REM 检查生成的文件
    if exist "target\x86_64-unknown-linux-gnu\debug\test-cross-compile" (
        echo ✅ 生成的 Linux 二进制文件: target\x86_64-unknown-linux-gnu\debug\test-cross-compile
        
        REM 显示文件信息
        for %%I in (target\x86_64-unknown-linux-gnu\debug\test-cross-compile) do (
            echo    文件大小: %%~zI bytes
        )
    )
) else (
    echo ❌ 交叉编译测试失败
    echo 💡 可能需要配置链接器，请检查 .cargo\config.toml
)

cd ..

REM 清理测试项目
rmdir /s /q test-cross-compile 2>nul

echo.
echo ✅ 交叉编译环境设置完成！
echo.
echo 📋 配置摘要:
echo   目标平台: x86_64-unknown-linux-gnu (Ubuntu 24)
echo   配置文件: .cargo\config.toml
echo   备份文件: .cargo\config.toml.backup (如果存在)
echo.
echo 🚀 现在可以运行:
echo   build.bat              - 普通构建
echo   build-obfuscated.bat   - 混淆构建
echo.
echo 💡 如果遇到链接错误，请:
echo   1. 安装 WSL 并在其中安装 build-essential
echo   2. 或安装 MinGW-w64
echo   3. 然后编辑 .cargo\config.toml 配置链接器

pause
