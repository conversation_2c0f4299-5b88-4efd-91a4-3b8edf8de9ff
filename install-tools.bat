@echo off
REM Walmart绑卡回调通知服务 - 开发工具安装脚本

echo 🛠️  Walmart 绑卡回调通知服务 - 开发工具安装
echo.

REM 检查管理员权限
net session >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 💡 请右键点击脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过

REM 检查是否安装了 Chocolatey
where choco >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 📦 安装 Chocolatey 包管理器...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Chocolatey 安装失败
        pause
        exit /b 1
    )
    echo ✅ Chocolatey 安装成功
) else (
    echo ✅ Chocolatey 已安装
)

REM 刷新环境变量
call refreshenv

REM 检查是否安装了 Rust
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 🦀 安装 Rust 工具链...
    choco install rust -y
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Rust 安装失败
        echo 💡 请手动访问 https://rustup.rs/ 安装 Rust
        pause
        exit /b 1
    )
    echo ✅ Rust 安装成功
) else (
    echo ✅ Rust 已安装
)

REM 刷新环境变量
call refreshenv

REM 安装交叉编译目标
echo 🎯 安装 Ubuntu 24 交叉编译目标...
rustup target add x86_64-unknown-linux-gnu
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 交叉编译目标安装失败
    pause
    exit /b 1
)
echo ✅ 交叉编译目标安装成功

REM 检查是否安装了 UPX
where upx >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 🗜️  安装 UPX 压缩工具...
    choco install upx -y
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  UPX 安装失败，将跳过压缩步骤
        echo 💡 可手动从 https://upx.github.io/ 下载 UPX
    ) else (
        echo ✅ UPX 安装成功
    )
) else (
    echo ✅ UPX 已安装
)

REM 检查是否安装了 Git
where git >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 📚 安装 Git...
    choco install git -y
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  Git 安装失败
        echo 💡 可手动从 https://git-scm.com/ 下载 Git
    ) else (
        echo ✅ Git 安装成功
    )
) else (
    echo ✅ Git 已安装
)

REM 安装有用的 Cargo 工具
echo 🔧 安装 Cargo 工具...

echo   📊 安装 cargo-audit (安全审计)...
cargo install cargo-audit --quiet
if %ERRORLEVEL% EQU 0 (
    echo   ✅ cargo-audit 安装成功
) else (
    echo   ⚠️  cargo-audit 安装失败
)

echo   🧹 安装 cargo-clean (清理工具)...
cargo install cargo-clean --quiet
if %ERRORLEVEL% EQU 0 (
    echo   ✅ cargo-clean 安装成功
) else (
    echo   ⚠️  cargo-clean 安装失败
)

echo   📈 安装 cargo-bloat (大小分析)...
cargo install cargo-bloat --quiet
if %ERRORLEVEL% EQU 0 (
    echo   ✅ cargo-bloat 安装成功
) else (
    echo   ⚠️  cargo-bloat 安装失败
)

REM 配置 Rust 镜像源 (中国用户)
echo 🌐 配置 Rust 镜像源...
if not exist "%USERPROFILE%\.cargo" mkdir "%USERPROFILE%\.cargo"

echo [source.crates-io] > "%USERPROFILE%\.cargo\config.toml"
echo registry = "https://github.com/rust-lang/crates.io-index" >> "%USERPROFILE%\.cargo\config.toml"
echo replace-with = "ustc" >> "%USERPROFILE%\.cargo\config.toml"
echo. >> "%USERPROFILE%\.cargo\config.toml"
echo [source.ustc] >> "%USERPROFILE%\.cargo\config.toml"
echo registry = "git://mirrors.ustc.edu.cn/crates.io-index" >> "%USERPROFILE%\.cargo\config.toml"

echo ✅ Rust 镜像源配置完成

REM 验证安装
echo.
echo 🔍 验证安装...
echo.

echo Rust 版本:
rustc --version
echo.

echo Cargo 版本:
cargo --version
echo.

echo 可用目标:
rustup target list --installed
echo.

if exist "%USERPROFILE%\.cargo\bin\upx.exe" (
    echo UPX 版本:
    upx --version
    echo.
)

echo.
echo ✅ 开发环境安装完成！
echo.
echo 🚀 现在可以运行构建脚本:
echo   普通构建: build.bat
echo   混淆构建: build-obfuscated.bat
echo.
echo 📚 有用的命令:
echo   cargo check          - 快速检查代码
echo   cargo test           - 运行测试
echo   cargo audit          - 安全审计
echo   cargo bloat --release - 分析二进制大小
echo.

pause
