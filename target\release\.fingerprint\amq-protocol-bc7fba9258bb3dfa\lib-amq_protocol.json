{"rustc": 1842507548689473721, "features": "[\"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\", \"verbose-errors\"]", "target": 12659125817264579830, "profile": 3592778941406178886, "path": 2008609970280484635, "deps": [[2985572863888970315, "amq_protocol_types", false, 1445438362556156904], [4886105269790530060, "cookie_factory", false, 1536489239733621174], [6502365400774175331, "nom", false, 15820068375576791178], [7048981225526245511, "build_script_build", false, 12349687708787198977], [9689903380558560274, "serde", false, 5616481245319993094], [11096876330329401515, "amq_protocol_uri", false, 4014177116918990465], [12404004217544788841, "amq_protocol_tcp", false, 18343467381128099318]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\amq-protocol-bc7fba9258bb3dfa\\dep-lib-amq_protocol", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}