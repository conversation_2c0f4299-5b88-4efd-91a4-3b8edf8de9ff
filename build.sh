#!/bin/bash

# Walmart绑卡回调通知服务构建脚本

set -e

echo "🦀 开始构建 Walmart 绑卡回调通知服务 (Rust版本)"

# 检查Rust环境
if ! command -v cargo &> /dev/null; then
    echo "❌ 错误: 未找到 Cargo，请先安装 Rust"
    exit 1
fi

echo "✅ Rust 环境检查通过"

# 清理之前的构建
echo "🧹 清理之前的构建..."
cargo clean

# 检查代码格式
echo "📝 检查代码格式..."
if command -v rustfmt &> /dev/null; then
    cargo fmt --check || {
        echo "⚠️  代码格式不符合标准，正在自动格式化..."
        cargo fmt
    }
fi

# 运行 Clippy 检查
echo "🔍 运行 Clippy 代码检查..."
if command -v clippy &> /dev/null; then
    cargo clippy -- -D warnings || {
        echo "⚠️  Clippy 检查发现问题，请修复后重试"
    }
fi

# 编译检查
echo "🔧 编译检查..."
cargo check

# 运行测试
echo "🧪 运行测试..."
cargo test

# 构建 Debug 版本
echo "🏗️  构建 Debug 版本..."
cargo build

# 构建 Release 版本
echo "🚀 构建 Release 版本..."
cargo build --release

echo "✅ 构建完成！"
echo ""
echo "📦 构建产物:"
echo "  Debug:   ./target/debug/walmart-bind-card-notify"
echo "  Release: ./target/release/walmart-bind-card-notify"
echo ""
echo "🚀 运行方式:"
echo "  开发环境: cargo run"
echo "  生产环境: ./target/release/walmart-bind-card-notify"
echo ""
echo "⚙️  配置文件: config.toml"
echo "📚 文档: README.md"
