{"rustc": 1842507548689473721, "features": "[\"acl\", \"aio\", \"async-trait\", \"bytes\", \"default\", \"futures-util\", \"geospatial\", \"keep-alive\", \"pin-project-lite\", \"script\", \"sha1_smol\", \"socket2\", \"streams\", \"tokio\", \"tokio-comp\", \"tokio-util\"]", "declared_features": "[\"acl\", \"ahash\", \"aio\", \"arc-swap\", \"async-native-tls\", \"async-std\", \"async-std-comp\", \"async-std-native-tls-comp\", \"async-std-rustls-comp\", \"async-std-tls-comp\", \"async-trait\", \"bytes\", \"cluster\", \"cluster-async\", \"connection-manager\", \"crc16\", \"default\", \"futures\", \"futures-rustls\", \"futures-util\", \"geospatial\", \"json\", \"keep-alive\", \"log\", \"native-tls\", \"pin-project-lite\", \"r2d2\", \"rand\", \"rustls\", \"rustls-native-certs\", \"rustls-pemfile\", \"rustls-webpki\", \"script\", \"sentinel\", \"serde\", \"serde_json\", \"sha1_smol\", \"socket2\", \"streams\", \"tcp_nodelay\", \"tls\", \"tls-native-tls\", \"tls-rustls\", \"tls-rustls-insecure\", \"tls-rustls-webpki-roots\", \"tokio\", \"tokio-comp\", \"tokio-native-tls\", \"tokio-native-tls-comp\", \"tokio-retry\", \"tokio-rustls\", \"tokio-rustls-comp\", \"tokio-util\", \"webpki-roots\"]", "target": 5936222214539778515, "profile": 3592778941406178886, "path": 16655232270486737950, "deps": [[40386456601120721, "percent_encoding", false, 15461567079426116486], [917570942013697716, "sha1_smol", false, 7467279147748709024], [1211321333142909612, "socket2", false, 5614160130517075156], [1216309103264968120, "ryu", false, 546641859128776644], [1906322745568073236, "pin_project_lite", false, 11022751310630360985], [3150220818285335163, "url", false, 3188455783886142279], [7695812897323945497, "itoa", false, 16880692167372541220], [10629569228670356391, "futures_util", false, 14323013482787026363], [11946729385090170470, "async_trait", false, 17291696389737625151], [15894030960229394068, "tokio_util", false, 7052716708192602676], [16066129441945555748, "bytes", false, 9094909366198335868], [17531218394775549125, "tokio", false, 2041435398497995185], [17915660048393766120, "combine", false, 3223677440247480454]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\redis-3f3b99b1feaf3a6e\\dep-lib-redis", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}