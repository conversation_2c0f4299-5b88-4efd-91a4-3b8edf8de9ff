{"rustc": 1842507548689473721, "features": "[\"default\", \"rustls\", \"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"serde_json\", \"vendored-openssl\"]", "target": 5047893427188600641, "profile": 3592778941406178886, "path": 205468854422028216, "deps": [[5103565458935487, "futures_io", false, 16531501961030068615], [1951382276944535576, "executor_trait", false, 13759581809213866419], [3395626199960367565, "pinky_swear", false, 5311663090489604042], [4495526598637097934, "parking_lot", false, 18323501347414722858], [4656928804077918400, "flume", false, 17603995623714557446], [4713603720635702932, "build_script_build", false, 11458354261337588889], [7048981225526245511, "amq_protocol", false, 6517717757425223424], [7620660491849607393, "futures_core", false, 7117656570195926816], [8606274917505247608, "tracing", false, 11372811433317039271], [8864093321401338808, "waker_fn", false, 5012661681864696724], [9689903380558560274, "serde", false, 5616481245319993094], [11946729385090170470, "async_trait", false, 17291696389737625151], [14332133141799632110, "reactor_trait", false, 17798832837608019769], [15121870802873242844, "async_global_executor_trait", false, 10627241329589672643], [16454787060997881635, "async_reactor_trait", false, 14441470335818838405]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\lapin-1b4ed66e5af40a27\\dep-lib-lapin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}