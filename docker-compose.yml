# Walmart绑卡回调通知服务 - Docker Compose配置

version: '3.8'

services:
  # 回调服务
  callback-service:
    build: .
    container_name: walmart-callback-service
    restart: unless-stopped
    environment:
      - RUST_LOG=info
      - WALMART_CALLBACK_DATABASE_URL=mysql://walmart:walmart123@mysql:3306/walmart_card_db
      - WALMART_CALLBACK_REDIS_URL=redis://redis:6379
      - WALMART_CALLBACK_RABBITMQ_URL=amqp://walmart:walmart123@rabbitmq:5672/%2f
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - walmart-network
    volumes:
      - ./logs:/var/log/walmart
    # ports:
    #   - "8080:8080"  # 如果有HTTP服务端口

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: walmart-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=walmart_card_db
      - MYSQL_USER=walmart
      - MYSQL_PASSWORD=walmart123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    networks:
      - walmart-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: walmart-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - walmart-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: walmart-rabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=walmart
      - RABBITMQ_DEFAULT_PASS=walmart123
    ports:
      - "5672:5672"   # AMQP端口
      - "15672:15672" # 管理界面端口
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - walmart-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      timeout: 30s
      retries: 5

  # 监控 - Prometheus (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: walmart-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - walmart-network
    profiles:
      - monitoring

  # 监控 - Grafana (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: walmart-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - walmart-network
    profiles:
      - monitoring

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  walmart-network:
    driver: bridge
