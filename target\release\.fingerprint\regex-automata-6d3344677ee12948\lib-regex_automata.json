{"rustc": 1842507548689473721, "features": "[\"default\", \"regex-syntax\", \"std\"]", "declared_features": "[\"default\", \"fst\", \"regex-syntax\", \"std\", \"transducer\"]", "target": 189779444668410301, "profile": 3592778941406178886, "path": 8973982168390051326, "deps": [[7982432068776955834, "regex_syntax", false, 680590456288874007]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-automata-6d3344677ee12948\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}