# Walmart 绑卡回调通知服务 - Rust 版本

高性能、内存安全的绑卡回调通知服务，使用 Rust 重写，解决重复回调问题。

## 🚀 特性

- **并发安全**: 编译时保证线程安全，防止数据竞争
- **防重复回调**: 基于 Redis 的分布式锁机制，彻底解决重复通知问题
- **高性能**: 零成本抽象，内存使用高效
- **可靠性**: 强类型系统，编译时错误检查
- **可观测性**: 结构化日志，健康检查，统计信息
- **混淆安全**: 显式字段映射，不依赖反射，支持代码混淆

## 📋 系统要求

- Rust 1.70+
- MySQL 8.0+
- Redis 6.0+
- RabbitMQ 3.8+

## 🛠️ 安装和构建

### Windows 开发环境快速设置

```batch
# 1. 安装开发工具 (管理员权限)
install-tools.bat

# 2. 设置交叉编译环境
setup-cross-compile.bat

# 3. 普通构建 (Windows + Ubuntu 24)
build.bat

# 4. 混淆构建 (高安全性)
build-obfuscated.bat
```

### 手动开发环境

```bash
# 克隆项目
git clone <repository-url>
cd walmart-bind-card-notify

# 安装 Rust (如果未安装)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 添加 Ubuntu 24 交叉编译目标
rustup target add x86_64-unknown-linux-gnu

# 安装依赖并构建
cargo build

# 运行测试
cargo test

# 开发模式运行
cargo run
```

### 生产环境构建

#### Windows 交叉编译到 Ubuntu 24

```batch
# 混淆版本 (推荐生产环境)
build-obfuscated.bat

# 普通版本
build.bat
```

#### Linux 本地构建

```bash
# 优化构建
cargo build --release --target x86_64-unknown-linux-gnu

# 构建后的二进制文件位于
./target/x86_64-unknown-linux-gnu/release/walmart-bind-card-notify
```

### Docker 构建

```dockerfile
FROM rust:1.70 as builder

WORKDIR /app
COPY . .
RUN cargo build --release

FROM ubuntu:24.04

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libmysqlclient21 \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/release/walmart-bind-card-notify /usr/local/bin/

CMD ["/usr/local/bin/walmart-bind-card-notify"]
```

## ⚙️ 配置

### 配置文件

复制 `config.toml` 并根据环境修改：

```toml
[database]
url = "mysql://user:password@localhost:3306/walmart_card_db"
max_connections = 10

[redis]
url = "redis://localhost:6379"

[rabbitmq]
url = "amqp://guest:guest@localhost:5672/%2f"
queue_name = "bind_card_callback_queue"

[callback]
max_retries = 3
timeout_seconds = 10
max_concurrent = 100
```

### 环境变量

也可以通过环境变量配置（优先级更高）：

```bash
export WALMART_CALLBACK_DATABASE_URL="mysql://..."
export WALMART_CALLBACK_REDIS_URL="redis://..."
export WALMART_CALLBACK_RABBITMQ_URL="amqp://..."
```

## 🚦 运行

### 直接运行

```bash
# 使用默认配置
./walmart-bind-card-notify

# 指定配置文件
CONFIG_PATH=production.toml ./walmart-bind-card-notify
```

### 系统服务

创建 systemd 服务文件 `/etc/systemd/system/walmart-callback.service`：

```ini
[Unit]
Description=Walmart Callback Service
After=network.target

[Service]
Type=simple
User=walmart
WorkingDirectory=/opt/walmart-callback
ExecStart=/opt/walmart-callback/walmart-bind-card-notify
Environment=CONFIG_PATH=/opt/walmart-callback/config.toml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl enable walmart-callback
sudo systemctl start walmart-callback
sudo systemctl status walmart-callback
```

## 🔍 监控和健康检查

### 健康检查端点

服务提供健康检查接口（如果启用 HTTP 服务器）：

```bash
curl http://localhost:8080/health
```

响应示例：

```json
{
  "status": "ok",
  "database": true,
  "redis": true,
  "queue": true,
  "overall": true
}
```

### 日志监控

服务使用结构化日志，支持 JSON 格式：

```bash
# 查看日志
journalctl -u walmart-callback -f

# 过滤错误日志
journalctl -u walmart-callback | grep ERROR
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**

   ```
   检查数据库URL和权限
   确保数据库服务正在运行
   ```

2. **Redis 连接失败**

   ```
   检查Redis URL和网络连接
   确保Redis服务正在运行
   ```

3. **消息队列连接失败**
   ```
   检查RabbitMQ URL和权限
   确保队列和交换机已正确配置
   ```

### 调试模式

启用详细日志：

```bash
RUST_LOG=debug ./walmart-bind-card-notify
```

## 🔧 开发

### 项目结构

```
src/
├── main.rs          # 主程序入口
├── lib.rs           # 库入口
├── config.rs        # 配置管理
├── models.rs        # 数据模型
├── database.rs      # 数据库访问
├── http.rs          # HTTP客户端
├── queue.rs         # 消息队列
├── services.rs      # 核心服务
├── utils.rs         # 工具函数
└── errors.rs        # 错误定义
```

### 添加新功能

1. 在相应模块中添加代码
2. 更新测试
3. 更新文档
4. 提交 PR

## 📊 性能

### 基准测试

```bash
# 运行基准测试
cargo bench
```

### 性能特点

- **内存使用**: 比 Python 版本减少 60-80%
- **CPU 使用**: 比 Python 版本减少 40-60%
- **并发处理**: 支持数千并发连接
- **响应时间**: 平均响应时间 < 10ms

## 🔒 安全特性

### 代码混淆和加密

#### 混淆版本特性

```batch
# Windows 混淆构建
build-obfuscated.bat
```

**安全保护级别**：

- ✅ **符号剥离**：完全移除调试符号和函数名
- ✅ **UPX 压缩加密**：二进制文件压缩和加密
- ✅ **静态链接**：无外部依赖，防止 DLL 劫持
- ✅ **编译时优化**：LTO + 单元优化，增加逆向难度
- ✅ **反调试保护**：panic=abort，防止调试器附加

#### 混淆安全特性

- **显式字段映射**：不依赖反射，混淆后仍能正确访问数据库
- **编译时 SQL 检查**：防止运行时 SQL 错误
- **常量化配置**：避免字符串字面量被混淆破坏
- **内存安全**：编译时保证，无空指针解引用和缓冲区溢出

#### 部署包结构

```
dist/ubuntu24-obfuscated/
├── walmart-bind-card-notify    # 高度混淆的执行文件
├── config.prod.toml            # 生产配置模板
├── start-secure.sh             # 安全启动脚本
├── install.sh                  # 自动安装脚本
└── README-OBFUSCATED.md        # 混淆版本说明
```

## 📝 许可证

[MIT License](LICENSE)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请联系开发团队或提交 Issue。
