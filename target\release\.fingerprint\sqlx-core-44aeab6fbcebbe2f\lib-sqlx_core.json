{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 3592778941406178886, "path": 8007707607423674868, "deps": [[5103565458935487, "futures_io", false, 16531501961030068615], [40386456601120721, "percent_encoding", false, 15461567079426116486], [530211389790465181, "hex", false, 17192918147484275718], [788558663644978524, "crossbeam_queue", false, 15804550659836401766], [966925859616469517, "ahash", false, 15938301076874898497], [1162433738665300155, "crc", false, 1297127895154801519], [1464803193346256239, "event_listener", false, 11646086803917144990], [1811549171721445101, "futures_channel", false, 15119990922335647927], [3150220818285335163, "url", false, 3188455783886142279], [3405817021026194662, "hashlink", false, 6941266834291055845], [3646857438214563691, "futures_intrusive", false, 6541074419992585040], [3666196340704888985, "smallvec", false, 73218954786341571], [3712811570531045576, "byteorder", false, 14046071016507914921], [3722963349756955755, "once_cell", false, 4623307841458457968], [5986029879202738730, "log", false, 3143729194340817726], [6493259146304816786, "indexmap", false, 5753417972539228703], [7620660491849607393, "futures_core", false, 7117656570195926816], [8008191657135824715, "thiserror", false, 11445770174315866089], [8319709847752024821, "uuid", false, 2060531924281077579], [8606274917505247608, "tracing", false, 11372811433317039271], [9689903380558560274, "serde", false, 5616481245319993094], [9857275760291862238, "sha2", false, 4590319456742638228], [9897246384292347999, "chrono", false, 8658754808873048319], [10629569228670356391, "futures_util", false, 14323013482787026363], [10862088793507253106, "sqlformat", false, 16272410254186214198], [11295624341523567602, "rustls", false, 4918743300688631855], [12170264697963848012, "either", false, 15592975620269386129], [15932120279885307830, "memchr", false, 5905481241054209797], [16066129441945555748, "bytes", false, 9094909366198335868], [16311359161338405624, "rustls_pemfile", false, 4444031905493527596], [16362055519698394275, "serde_json", false, 17317803994796681169], [16973251432615581304, "tokio_stream", false, 17355002553849995092], [17106256174509013259, "atoi", false, 8269770246313000702], [17531218394775549125, "tokio", false, 2041435398497995185], [17605717126308396068, "paste", false, 11364282378793036223], [17652733826348741533, "webpki_roots", false, 4771853171218649805]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-core-44aeab6fbcebbe2f\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}