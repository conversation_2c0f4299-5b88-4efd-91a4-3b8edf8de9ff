{"rustc": 1842507548689473721, "features": "[\"p12-keystore\", \"rustls-common\", \"rustls-connector\", \"rustls-native-certs\", \"rustls-pemfile\"]", "declared_features": "[\"default\", \"native-tls\", \"openssl\", \"p12-keystore\", \"rustls\", \"rustls-common\", \"rustls-connector\", \"rustls-native-certs\", \"rustls-pemfile\", \"rustls-webpki-roots-certs\", \"vendored-openssl\"]", "target": 7653913482476367865, "profile": 3592778941406178886, "path": 16006226800062280087, "deps": [[2828590642173593838, "cfg_if", false, 7864105803641503796], [12343627582788294196, "p12_keystore", false, 4295838417884844593], [12449878899851902631, "rustls_connector", false, 17239589655676668047], [15032952994102373905, "rustls_pemfile", false, 1664641995864143753]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tcp-stream-4377e448efb8c6d3\\dep-lib-tcp_stream", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}