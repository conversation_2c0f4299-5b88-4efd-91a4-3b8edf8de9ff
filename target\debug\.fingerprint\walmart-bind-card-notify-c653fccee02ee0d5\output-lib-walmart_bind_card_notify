{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":1107,"byte_end":1723,"line_start":33,"line_end":50,"column_start":22,"column_end":10,"is_primary":false,"text":[{"text":"        let record = sqlx::query_as!(","highlight_start":22,"highlight_end":38},{"text":"            CardRecord,","highlight_start":1,"highlight_end":24},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            SELECT ","highlight_start":1,"highlight_end":20},{"text":"                id as \"record_id\",","highlight_start":1,"highlight_end":35},{"text":"                merchant_id as \"merchant_identifier: u64\", ","highlight_start":1,"highlight_end":60},{"text":"                merchant_order_id as \"order_identifier\",","highlight_start":1,"highlight_end":57},{"text":"                card_number as \"card_num\",","highlight_start":1,"highlight_end":43},{"text":"                status as \"current_status\",","highlight_start":1,"highlight_end":44},{"text":"                callback_status as \"callback_state\",","highlight_start":1,"highlight_end":53},{"text":"                amount as \"order_amount: u64\",","highlight_start":1,"highlight_end":47},{"text":"                created_at as \"creation_time\",","highlight_start":1,"highlight_end":47},{"text":"                updated_at as \"modification_time\"","highlight_start":1,"highlight_end":50},{"text":"            FROM card_records ","highlight_start":1,"highlight_end":31},{"text":"            WHERE id = ?","highlight_start":1,"highlight_end":25},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            record_id","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26452,"byte_end":26473,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:33:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let record = sqlx::query_as!(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            CardRecord,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            SELECT \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            record_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":2050,"byte_end":2734,"line_start":63,"line_end":81,"column_start":22,"column_end":10,"is_primary":false,"text":[{"text":"        let record = sqlx::query_as!(","highlight_start":22,"highlight_end":38},{"text":"            CardRecord,","highlight_start":1,"highlight_end":24},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            SELECT ","highlight_start":1,"highlight_end":20},{"text":"                id as \"record_id\",","highlight_start":1,"highlight_end":35},{"text":"                merchant_id as \"merchant_identifier: u64\", ","highlight_start":1,"highlight_end":60},{"text":"                merchant_order_id as \"order_identifier\",","highlight_start":1,"highlight_end":57},{"text":"                card_number as \"card_num\",","highlight_start":1,"highlight_end":43},{"text":"                status as \"current_status\",","highlight_start":1,"highlight_end":44},{"text":"                callback_status as \"callback_state\",","highlight_start":1,"highlight_end":53},{"text":"                amount as \"order_amount: u64\",","highlight_start":1,"highlight_end":47},{"text":"                created_at as \"creation_time\",","highlight_start":1,"highlight_end":47},{"text":"                updated_at as \"modification_time\"","highlight_start":1,"highlight_end":50},{"text":"            FROM card_records ","highlight_start":1,"highlight_end":31},{"text":"            WHERE merchant_id = ? AND merchant_order_id = ?","highlight_start":1,"highlight_end":60},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            merchant_id,","highlight_start":1,"highlight_end":25},{"text":"            merchant_order_id","highlight_start":1,"highlight_end":30},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26452,"byte_end":26473,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:63:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let record = sqlx::query_as!(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            CardRecord,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            SELECT \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            merchant_order_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":3019,"byte_end":3185,"line_start":94,"line_end":98,"column_start":22,"column_end":10,"is_primary":false,"text":[{"text":"        let result = sqlx::query!(","highlight_start":22,"highlight_end":35},{"text":"            \"UPDATE card_records SET callback_status = ?, updated_at = NOW() WHERE id = ?\",","highlight_start":1,"highlight_end":92},{"text":"            status.as_str(),","highlight_start":1,"highlight_end":29},{"text":"            record_id","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:94:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let result = sqlx::query!(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"UPDATE card_records SET callback_status = ?, updated_at = NOW() WHERE id = ?\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m96\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            status.as_str(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m97\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            record_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":3434,"byte_end":3746,"line_start":107,"line_end":118,"column_start":24,"column_end":10,"is_primary":false,"text":[{"text":"        let merchant = sqlx::query_as!(","highlight_start":24,"highlight_end":40},{"text":"            Merchant,","highlight_start":1,"highlight_end":22},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            SELECT ","highlight_start":1,"highlight_end":20},{"text":"                id as \"merchant_id: u64\",","highlight_start":1,"highlight_end":42},{"text":"                name as \"merchant_name\",","highlight_start":1,"highlight_end":41},{"text":"                callback_url as \"callback_endpoint\"","highlight_start":1,"highlight_end":52},{"text":"            FROM merchants ","highlight_start":1,"highlight_end":28},{"text":"            WHERE id = ?","highlight_start":1,"highlight_end":25},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            merchant_id","highlight_start":1,"highlight_end":24},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26452,"byte_end":26473,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:107:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m107\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let merchant = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Merchant,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            SELECT \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            merchant_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":3985,"byte_end":4099,"line_start":127,"line_end":130,"column_start":19,"column_end":10,"is_primary":false,"text":[{"text":"        let row = sqlx::query!(","highlight_start":19,"highlight_end":32},{"text":"            \"SELECT callback_status FROM card_records WHERE id = ?\",","highlight_start":1,"highlight_end":69},{"text":"            record_id","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:127:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let row = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ___________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m128\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"SELECT callback_status FROM card_records WHERE id = ?\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            record_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26674,"byte_end":26767,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":4489,"byte_end":5285,"line_start":142,"line_end":163,"column_start":23,"column_end":10,"is_primary":false,"text":[{"text":"        let records = sqlx::query_as!(","highlight_start":23,"highlight_end":39},{"text":"            CardRecord,","highlight_start":1,"highlight_end":24},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            SELECT ","highlight_start":1,"highlight_end":20},{"text":"                id as \"record_id\",","highlight_start":1,"highlight_end":35},{"text":"                merchant_id as \"merchant_identifier: u64\", ","highlight_start":1,"highlight_end":60},{"text":"                merchant_order_id as \"order_identifier\",","highlight_start":1,"highlight_end":57},{"text":"                card_number as \"card_num\",","highlight_start":1,"highlight_end":43},{"text":"                status as \"current_status\",","highlight_start":1,"highlight_end":44},{"text":"                callback_status as \"callback_state\",","highlight_start":1,"highlight_end":53},{"text":"                amount as \"order_amount: u64\",","highlight_start":1,"highlight_end":47},{"text":"                created_at as \"creation_time\",","highlight_start":1,"highlight_end":47},{"text":"                updated_at as \"modification_time\"","highlight_start":1,"highlight_end":50},{"text":"            FROM card_records ","highlight_start":1,"highlight_end":31},{"text":"            WHERE callback_status = ? ","highlight_start":1,"highlight_end":39},{"text":"            AND updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)","highlight_start":1,"highlight_end":64},{"text":"            ORDER BY updated_at ASC","highlight_start":1,"highlight_end":36},{"text":"            LIMIT ?","highlight_start":1,"highlight_end":20},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            db_constants::CALLBACK_STATUS_FAILED,","highlight_start":1,"highlight_end":50},{"text":"            limit","highlight_start":1,"highlight_end":18},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":26452,"byte_end":26473,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:142:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let records = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            CardRecord,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            SELECT \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            limit\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":6497,"byte_end":6622,"line_start":210,"line_end":213,"column_start":30,"column_end":10,"is_primary":false,"text":[{"text":"        let current_status = sqlx::query!(","highlight_start":30,"highlight_end":43},{"text":"            \"SELECT callback_status FROM card_records WHERE id = ? FOR UPDATE\",","highlight_start":1,"highlight_end":80},{"text":"            record_id","highlight_start":1,"highlight_end":22},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:210:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let current_status = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m211\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"SELECT callback_status FROM card_records WHERE id = ? FOR UPDATE\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            record_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":16656,"byte_end":16727,"line_start":332,"line_end":332,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\database.rs","byte_start":6869,"byte_end":7071,"line_start":220,"line_end":224,"column_start":30,"column_end":18,"is_primary":false,"text":[{"text":"                let result = sqlx::query!(","highlight_start":30,"highlight_end":43},{"text":"                    \"UPDATE card_records SET callback_status = ?, updated_at = NOW() WHERE id = ?\",","highlight_start":1,"highlight_end":100},{"text":"                    new_status.as_str(),","highlight_start":1,"highlight_end":41},{"text":"                    record_id","highlight_start":1,"highlight_end":30},{"text":"                )","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.7.4\\src\\macros\\mod.rs","byte_start":15748,"byte_end":15766,"line_start":318,"line_end":318,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.7.4\\src\\lib.rs","byte_start":89,"byte_end":143,"line_start":8,"line_end":8,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:220:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m220\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                let result = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    \"UPDATE card_records SET callback_status = ?, updated_at = NOW() WHERE id = ?\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    new_status.as_str(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m223\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    record_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m224\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `Duration` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\utils.rs","byte_start":8019,"byte_end":8027,"line_start":249,"line_end":249,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    pub fn calculate_backoff_delay(retry_count: u32, base_delay: Duration, max_delay: Duration) -> Duration {","highlight_start":66,"highlight_end":74}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these items","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use crate::utils::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use std::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use chrono::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use tokio::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `Duration` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:249:66\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m(retry_count: u32, base_delay: Duration, max_delay: Duration) -> Duration {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these items\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse crate::utils::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse std::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse chrono::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse tokio::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `Duration` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\utils.rs","byte_start":8040,"byte_end":8048,"line_start":249,"line_end":249,"column_start":87,"column_end":95,"is_primary":true,"text":[{"text":"    pub fn calculate_backoff_delay(retry_count: u32, base_delay: Duration, max_delay: Duration) -> Duration {","highlight_start":87,"highlight_end":95}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these items","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use crate::utils::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use std::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use chrono::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use tokio::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `Duration` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:249:87\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mse_delay: Duration, max_delay: Duration) -> Duration {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these items\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse crate::utils::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse std::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse chrono::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse tokio::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `Duration` in this scope","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"src\\utils.rs","byte_start":8053,"byte_end":8061,"line_start":249,"line_end":249,"column_start":100,"column_end":108,"is_primary":true,"text":[{"text":"    pub fn calculate_backoff_delay(retry_count: u32, base_delay: Duration, max_delay: Duration) -> Duration {","highlight_start":100,"highlight_end":108}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these items","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use crate::utils::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use std::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use chrono::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use tokio::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot find type `Duration` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:249:100\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mation, max_delay: Duration) -> Duration {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these items\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse crate::utils::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse std::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse chrono::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse tokio::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of undeclared type `Duration`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\utils.rs","byte_start":8156,"byte_end":8164,"line_start":251,"line_end":251,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"        let delay = Duration::from_secs(delay_secs);","highlight_start":21,"highlight_end":29}],"label":"use of undeclared type `Duration`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these items","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use crate::utils::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use std::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use chrono::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src\\utils.rs","byte_start":7419,"byte_end":7419,"line_start":232,"line_end":232,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    use sha2::{Sha256, Digest};","highlight_start":5,"highlight_end":5}],"label":null,"suggested_replacement":"use tokio::time::Duration;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of undeclared type `Duration`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:251:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let delay = Duration::from_secs(delay_secs);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of undeclared type `Duration`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these items\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse crate::utils::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse std::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse chrono::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10muse tokio::time::Duration;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models.rs","byte_start":82,"byte_end":92,"line_start":4,"line_end":4,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models.rs","byte_start":78,"byte_end":94,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `CallbackResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\services.rs","byte_start":191,"byte_end":207,"line_start":5,"line_end":5,"column_start":39,"column_end":55,"is_primary":true,"text":[{"text":"use crate::http::{CallbackHttpClient, CallbackResponse};","highlight_start":39,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\services.rs","byte_start":189,"byte_end":207,"line_start":5,"line_end":5,"column_start":37,"column_end":55,"is_primary":true,"text":[{"text":"use crate::http::{CallbackHttpClient, CallbackResponse};","highlight_start":37,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services.rs","byte_start":170,"byte_end":171,"line_start":5,"line_end":5,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use crate::http::{CallbackHttpClient, CallbackResponse};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\services.rs","byte_start":207,"byte_end":208,"line_start":5,"line_end":5,"column_start":55,"column_end":56,"is_primary":true,"text":[{"text":"use crate::http::{CallbackHttpClient, CallbackResponse};","highlight_start":55,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `CallbackResponse`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services.rs:5:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::http::{CallbackHttpClient, CallbackResponse};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `CallbackError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\database.rs","byte_start":77,"byte_end":90,"line_start":3,"line_end":3,"column_start":21,"column_end":34,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":21,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\database.rs","byte_start":77,"byte_end":92,"line_start":3,"line_end":3,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\database.rs","byte_start":76,"byte_end":77,"line_start":3,"line_end":3,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\database.rs","byte_start":106,"byte_end":107,"line_start":3,"line_end":3,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `CallbackError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:3:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::errors::{CallbackError, CallbackResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Row`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\database.rs","byte_start":204,"byte_end":207,"line_start":5,"line_end":5,"column_start":23,"column_end":26,"is_primary":true,"text":[{"text":"use sqlx::{MySqlPool, Row};","highlight_start":23,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\database.rs","byte_start":202,"byte_end":207,"line_start":5,"line_end":5,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use sqlx::{MySqlPool, Row};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\database.rs","byte_start":192,"byte_end":193,"line_start":5,"line_end":5,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use sqlx::{MySqlPool, Row};","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\database.rs","byte_start":207,"byte_end":208,"line_start":5,"line_end":5,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use sqlx::{MySqlPool, Row};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Row`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:5:23\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sqlx::{MySqlPool, Row};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\database.rs","byte_start":214,"byte_end":233,"line_start":6,"line_end":6,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\database.rs","byte_start":210,"byte_end":235,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"use tracing::{info, warn, error};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::time::Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `CallbackError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\queue.rs","byte_start":94,"byte_end":107,"line_start":3,"line_end":3,"column_start":21,"column_end":34,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":21,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\queue.rs","byte_start":94,"byte_end":109,"line_start":3,"line_end":3,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\queue.rs","byte_start":93,"byte_end":94,"line_start":3,"line_end":3,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\queue.rs","byte_start":123,"byte_end":124,"line_start":3,"line_end":3,"column_start":50,"column_end":51,"is_primary":true,"text":[{"text":"use crate::errors::{CallbackError, CallbackResult};","highlight_start":50,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `CallbackError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\queue.rs:3:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::errors::{CallbackError, CallbackResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Consumer`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\queue.rs","byte_start":275,"byte_end":283,"line_start":7,"line_end":7,"column_start":27,"column_end":35,"is_primary":true,"text":[{"text":"    ConnectionProperties, Consumer, ExchangeKind,","highlight_start":27,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\queue.rs","byte_start":273,"byte_end":283,"line_start":7,"line_end":7,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"    ConnectionProperties, Consumer, ExchangeKind,","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Consumer`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\queue.rs:7:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ConnectionProperties, Consumer, ExchangeKind,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Response`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\http.rs","byte_start":191,"byte_end":199,"line_start":5,"line_end":5,"column_start":23,"column_end":31,"is_primary":true,"text":[{"text":"use reqwest::{Client, Response, StatusCode};","highlight_start":23,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\http.rs","byte_start":189,"byte_end":199,"line_start":5,"line_end":5,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"use reqwest::{Client, Response, StatusCode};","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Response`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\http.rs:5:23\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse reqwest::{Client, Response, StatusCode};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `serde_json::Value`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\http.rs","byte_start":218,"byte_end":235,"line_start":6,"line_end":6,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"use serde_json::Value;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\http.rs","byte_start":214,"byte_end":237,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json::Value;","highlight_start":1,"highlight_end":23},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `serde_json::Value`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\http.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::Value;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `message`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\services.rs","byte_start":11702,"byte_end":11709,"line_start":315,"line_end":315,"column_start":45,"column_end":52,"is_primary":true,"text":[{"text":"    async fn handle_callback_message(&self, message: CallbackMessage) -> CallbackResult<()> {","highlight_start":45,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\services.rs","byte_start":11702,"byte_end":11709,"line_start":315,"line_end":315,"column_start":45,"column_end":52,"is_primary":true,"text":[{"text":"    async fn handle_callback_message(&self, message: CallbackMessage) -> CallbackResult<()> {","highlight_start":45,"highlight_end":52}],"label":null,"suggested_replacement":"_message","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `message`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services.rs:315:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m315\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn handle_callback_message(&self, message: CallbackMessage) -> CallbackResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_message`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`Consumer` is not an iterator","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\queue.rs","byte_start":3068,"byte_end":3077,"line_start":100,"line_end":100,"column_start":46,"column_end":55,"is_primary":true,"text":[{"text":"            let mut consumer_iter = consumer.into_iter();","highlight_start":46,"highlight_end":55}],"label":"`Consumer` is not an iterator","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lapin-2.5.4\\src\\consumer.rs","byte_start":4943,"byte_end":4962,"line_start":137,"line_end":137,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct Consumer {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `Consumer: IntoIterator` or `Consumer: Iterator`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`Consumer: Iterator`\nwhich is required by `Consumer: IntoIterator`\n`&Consumer: Iterator`\nwhich is required by `&Consumer: IntoIterator`\n`&mut Consumer: Iterator`\nwhich is required by `&mut Consumer: IntoIterator`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Consumer` is not an iterator\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\queue.rs:100:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut consumer_iter = consumer.into_iter();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`Consumer` is not an iterator\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\lapin-2.5.4\\src\\consumer.rs:137:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Consumer {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mdoesn't satisfy `Consumer: IntoIterator` or `Consumer: Iterator`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `Consumer: Iterator`\u001b[0m\n\u001b[0m            which is required by `Consumer: IntoIterator`\u001b[0m\n\u001b[0m            `&Consumer: Iterator`\u001b[0m\n\u001b[0m            which is required by `&Consumer: IntoIterator`\u001b[0m\n\u001b[0m            `&mut Consumer: Iterator`\u001b[0m\n\u001b[0m            which is required by `&mut Consumer: IntoIterator`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `ping` found for struct `redis::aio::Connection` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils.rs","byte_start":816,"byte_end":820,"line_start":24,"line_end":24,"column_start":30,"column_end":34,"is_primary":true,"text":[{"text":"        let _: String = conn.ping().await?;","highlight_start":30,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `xpending` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\macros.rs","byte_start":6752,"byte_end":7021,"line_start":162,"line_end":167,"column_start":17,"column_end":40,"is_primary":true,"text":[{"text":"                fn $name<$lifetime, $($tyargs: $ty + Send + Sync + $lifetime,)* RV>(","highlight_start":17,"highlight_end":85},{"text":"                    & $lifetime mut self","highlight_start":1,"highlight_end":41},{"text":"                    $(, $argname: $argty)*","highlight_start":1,"highlight_end":43},{"text":"                ) -> crate::types::RedisFuture<'a, RV>","highlight_start":1,"highlight_end":55},{"text":"                where","highlight_start":1,"highlight_end":22},{"text":"                    RV: FromRedisValue,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\mod.rs","byte_start":2151,"byte_end":65818,"line_start":41,"line_end":1873,"column_start":1,"column_end":2,"is_primary":false,"text":[{"text":"implement_commands! {","highlight_start":1,"highlight_end":1},{"text":"    'a","highlight_start":1,"highlight_end":1},{"text":"    // most common operations","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the value of a key.  If key is a vec this becomes an `MGET`.","highlight_start":1,"highlight_end":1},{"text":"    fn get<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(if key.is_single_arg() { \"GET\" } else { \"MGET\" }).arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get values of keys","highlight_start":1,"highlight_end":1},{"text":"    fn mget<K: ToRedisArgs>(key: K){","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"MGET\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all keys matching pattern","highlight_start":1,"highlight_end":1},{"text":"    fn keys<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"KEYS\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the string value of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn set<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SET\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the string value of a key with options.","highlight_start":1,"highlight_end":1},{"text":"    fn set_options<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, options: SetOptions) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SET\").arg(key).arg(value).arg(options)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets multiple keys to their values.","highlight_start":1,"highlight_end":1},{"text":"    #[allow(deprecated)]","highlight_start":1,"highlight_end":1},{"text":"    #[deprecated(since = \"0.22.4\", note = \"Renamed to mset() to reflect Redis name\")]","highlight_start":1,"highlight_end":1},{"text":"    fn set_multiple<K: ToRedisArgs, V: ToRedisArgs>(items: &'a [(K, V)]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"MSET\").arg(items)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets multiple keys to their values.","highlight_start":1,"highlight_end":1},{"text":"    fn mset<K: ToRedisArgs, V: ToRedisArgs>(items: &'a [(K, V)]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"MSET\").arg(items)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the value and expiration of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn set_ex<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, seconds: u64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SETEX\").arg(key).arg(seconds).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the value and expiration in milliseconds of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn pset_ex<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, milliseconds: u64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PSETEX\").arg(key).arg(milliseconds).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the value of a key, only if the key does not exist","highlight_start":1,"highlight_end":1},{"text":"    fn set_nx<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SETNX\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets multiple keys to their values failing if at least one already exists.","highlight_start":1,"highlight_end":1},{"text":"    fn mset_nx<K: ToRedisArgs, V: ToRedisArgs>(items: &'a [(K, V)]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"MSETNX\").arg(items)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the string value of a key and return its old value.","highlight_start":1,"highlight_end":1},{"text":"    fn getset<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GETSET\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get a range of bytes/substring from the value of a key. Negative values provide an offset from the end of the value.","highlight_start":1,"highlight_end":1},{"text":"    fn getrange<K: ToRedisArgs>(key: K, from: isize, to: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GETRANGE\").arg(key).arg(from).arg(to)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Overwrite the part of the value stored in key at the specified offset.","highlight_start":1,"highlight_end":1},{"text":"    fn setrange<K: ToRedisArgs, V: ToRedisArgs>(key: K, offset: isize, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SETRANGE\").arg(key).arg(offset).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Delete one or more keys.","highlight_start":1,"highlight_end":1},{"text":"    fn del<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"DEL\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine if a key exists.","highlight_start":1,"highlight_end":1},{"text":"    fn exists<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"EXISTS\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine the type of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn key_type<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"TYPE\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set a key's time to live in seconds.","highlight_start":1,"highlight_end":1},{"text":"    fn expire<K: ToRedisArgs>(key: K, seconds: i64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"EXPIRE\").arg(key).arg(seconds)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the expiration for a key as a UNIX timestamp.","highlight_start":1,"highlight_end":1},{"text":"    fn expire_at<K: ToRedisArgs>(key: K, ts: i64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"EXPIREAT\").arg(key).arg(ts)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set a key's time to live in milliseconds.","highlight_start":1,"highlight_end":1},{"text":"    fn pexpire<K: ToRedisArgs>(key: K, ms: i64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PEXPIRE\").arg(key).arg(ms)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the expiration for a key as a UNIX timestamp in milliseconds.","highlight_start":1,"highlight_end":1},{"text":"    fn pexpire_at<K: ToRedisArgs>(key: K, ts: i64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PEXPIREAT\").arg(key).arg(ts)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove the expiration from a key.","highlight_start":1,"highlight_end":1},{"text":"    fn persist<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PERSIST\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the expiration time of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn ttl<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"TTL\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the expiration time of a key in milliseconds.","highlight_start":1,"highlight_end":1},{"text":"    fn pttl<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PTTL\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the value of a key and set expiration","highlight_start":1,"highlight_end":1},{"text":"    fn get_ex<K: ToRedisArgs>(key: K, expire_at: Expiry) {","highlight_start":1,"highlight_end":1},{"text":"        let (option, time_arg) = match expire_at {","highlight_start":1,"highlight_end":1},{"text":"            Expiry::EX(sec) => (\"EX\", Some(sec)),","highlight_start":1,"highlight_end":1},{"text":"            Expiry::PX(ms) => (\"PX\", Some(ms)),","highlight_start":1,"highlight_end":1},{"text":"            Expiry::EXAT(timestamp_sec) => (\"EXAT\", Some(timestamp_sec)),","highlight_start":1,"highlight_end":1},{"text":"            Expiry::PXAT(timestamp_ms) => (\"PXAT\", Some(timestamp_ms)),","highlight_start":1,"highlight_end":1},{"text":"            Expiry::PERSIST => (\"PERSIST\", None),","highlight_start":1,"highlight_end":1},{"text":"        };","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GETEX\").arg(key).arg(option).arg(time_arg)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the value of a key and delete it","highlight_start":1,"highlight_end":1},{"text":"    fn get_del<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GETDEL\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Rename a key.","highlight_start":1,"highlight_end":1},{"text":"    fn rename<K: ToRedisArgs, N: ToRedisArgs>(key: K, new_key: N) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"RENAME\").arg(key).arg(new_key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Rename a key, only if the new key does not exist.","highlight_start":1,"highlight_end":1},{"text":"    fn rename_nx<K: ToRedisArgs, N: ToRedisArgs>(key: K, new_key: N) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"RENAMENX\").arg(key).arg(new_key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unlink one or more keys.","highlight_start":1,"highlight_end":1},{"text":"    fn unlink<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"UNLINK\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // common string operations","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Append a value to a key.","highlight_start":1,"highlight_end":1},{"text":"    fn append<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"APPEND\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Increment the numeric value of a key by the given amount.  This","highlight_start":1,"highlight_end":1},{"text":"    /// issues a `INCRBY` or `INCRBYFLOAT` depending on the type.","highlight_start":1,"highlight_end":1},{"text":"    fn incr<K: ToRedisArgs, V: ToRedisArgs>(key: K, delta: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(if delta.describe_numeric_behavior() == NumericBehavior::NumberIsFloat {","highlight_start":1,"highlight_end":1},{"text":"            \"INCRBYFLOAT\"","highlight_start":1,"highlight_end":1},{"text":"        } else {","highlight_start":1,"highlight_end":1},{"text":"            \"INCRBY\"","highlight_start":1,"highlight_end":1},{"text":"        }).arg(key).arg(delta)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Decrement the numeric value of a key by the given amount.","highlight_start":1,"highlight_end":1},{"text":"    fn decr<K: ToRedisArgs, V: ToRedisArgs>(key: K, delta: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"DECRBY\").arg(key).arg(delta)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets or clears the bit at offset in the string value stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn setbit<K: ToRedisArgs>(key: K, offset: usize, value: bool) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SETBIT\").arg(key).arg(offset).arg(i32::from(value))","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the bit value at offset in the string value stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn getbit<K: ToRedisArgs>(key: K, offset: usize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GETBIT\").arg(key).arg(offset)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count set bits in a string.","highlight_start":1,"highlight_end":1},{"text":"    fn bitcount<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BITCOUNT\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count set bits in a string in a range.","highlight_start":1,"highlight_end":1},{"text":"    fn bitcount_range<K: ToRedisArgs>(key: K, start: usize, end: usize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BITCOUNT\").arg(key).arg(start).arg(end)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise AND between multiple keys (containing string values)","highlight_start":1,"highlight_end":1},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":1},{"text":"    fn bit_and<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BITOP\").arg(\"AND\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise OR between multiple keys (containing string values)","highlight_start":1,"highlight_end":1},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":1},{"text":"    fn bit_or<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BITOP\").arg(\"OR\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise XOR between multiple keys (containing string values)","highlight_start":1,"highlight_end":1},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":1},{"text":"    fn bit_xor<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BITOP\").arg(\"XOR\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise NOT of the key (containing string values)","highlight_start":1,"highlight_end":1},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":1},{"text":"    fn bit_not<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckey: S) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BITOP\").arg(\"NOT\").arg(dstkey).arg(srckey)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the length of the value stored in a key.","highlight_start":1,"highlight_end":1},{"text":"    fn strlen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"STRLEN\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // hash operations","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets a single (or multiple) fields from a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hget<K: ToRedisArgs, F: ToRedisArgs>(key: K, field: F) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(if field.is_single_arg() { \"HGET\" } else { \"HMGET\" }).arg(key).arg(field)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Deletes a single (or multiple) fields from a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hdel<K: ToRedisArgs, F: ToRedisArgs>(key: K, field: F) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HDEL\").arg(key).arg(field)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets a single field in a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hset<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(key: K, field: F, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HSET\").arg(key).arg(field).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets a single field in a hash if it does not exist.","highlight_start":1,"highlight_end":1},{"text":"    fn hset_nx<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(key: K, field: F, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HSETNX\").arg(key).arg(field).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets a multiple fields in a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hset_multiple<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(key: K, items: &'a [(F, V)]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HMSET\").arg(key).arg(items)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Increments a value.","highlight_start":1,"highlight_end":1},{"text":"    fn hincr<K: ToRedisArgs, F: ToRedisArgs, D: ToRedisArgs>(key: K, field: F, delta: D) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(if delta.describe_numeric_behavior() == NumericBehavior::NumberIsFloat {","highlight_start":1,"highlight_end":1},{"text":"            \"HINCRBYFLOAT\"","highlight_start":1,"highlight_end":1},{"text":"        } else {","highlight_start":1,"highlight_end":1},{"text":"            \"HINCRBY\"","highlight_start":1,"highlight_end":1},{"text":"        }).arg(key).arg(field).arg(delta)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Checks if a field in a hash exists.","highlight_start":1,"highlight_end":1},{"text":"    fn hexists<K: ToRedisArgs, F: ToRedisArgs>(key: K, field: F) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HEXISTS\").arg(key).arg(field)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all the keys in a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hkeys<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HKEYS\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all the values in a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hvals<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HVALS\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all the fields and values in a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hgetall<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HGETALL\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets the length of a hash.","highlight_start":1,"highlight_end":1},{"text":"    fn hlen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"HLEN\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // list operations","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop an element from a list, push it to another list","highlight_start":1,"highlight_end":1},{"text":"    /// and return it; or block until one is available","highlight_start":1,"highlight_end":1},{"text":"    fn blmove<S: ToRedisArgs, D: ToRedisArgs>(srckey: S, dstkey: D, src_dir: Direction, dst_dir: Direction, timeout: f64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BLMOVE\").arg(srckey).arg(dstkey).arg(src_dir).arg(dst_dir).arg(timeout)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pops `count` elements from the first non-empty list key from the list of","highlight_start":1,"highlight_end":1},{"text":"    /// provided key names; or blocks until one is available.","highlight_start":1,"highlight_end":1},{"text":"    fn blmpop<K: ToRedisArgs>(timeout: f64, numkeys: usize, key: K, dir: Direction, count: usize){","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BLMPOP\").arg(timeout).arg(numkeys).arg(key).arg(dir).arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove and get the first element in a list, or block until one is available.","highlight_start":1,"highlight_end":1},{"text":"    fn blpop<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BLPOP\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove and get the last element in a list, or block until one is available.","highlight_start":1,"highlight_end":1},{"text":"    fn brpop<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BRPOP\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop a value from a list, push it to another list and return it;","highlight_start":1,"highlight_end":1},{"text":"    /// or block until one is available.","highlight_start":1,"highlight_end":1},{"text":"    fn brpoplpush<S: ToRedisArgs, D: ToRedisArgs>(srckey: S, dstkey: D, timeout: f64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BRPOPLPUSH\").arg(srckey).arg(dstkey).arg(timeout)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get an element from a list by its index.","highlight_start":1,"highlight_end":1},{"text":"    fn lindex<K: ToRedisArgs>(key: K, index: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LINDEX\").arg(key).arg(index)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert an element before another element in a list.","highlight_start":1,"highlight_end":1},{"text":"    fn linsert_before<K: ToRedisArgs, P: ToRedisArgs, V: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"            key: K, pivot: P, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LINSERT\").arg(key).arg(\"BEFORE\").arg(pivot).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert an element after another element in a list.","highlight_start":1,"highlight_end":1},{"text":"    fn linsert_after<K: ToRedisArgs, P: ToRedisArgs, V: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"            key: K, pivot: P, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LINSERT\").arg(key).arg(\"AFTER\").arg(pivot).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the length of the list stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn llen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LLEN\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop an element a list, push it to another list and return it","highlight_start":1,"highlight_end":1},{"text":"    fn lmove<S: ToRedisArgs, D: ToRedisArgs>(srckey: S, dstkey: D, src_dir: Direction, dst_dir: Direction) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LMOVE\").arg(srckey).arg(dstkey).arg(src_dir).arg(dst_dir)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pops `count` elements from the first non-empty list key from the list of","highlight_start":1,"highlight_end":1},{"text":"    /// provided key names.","highlight_start":1,"highlight_end":1},{"text":"    fn lmpop<K: ToRedisArgs>( numkeys: usize, key: K, dir: Direction, count: usize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LMPOP\").arg(numkeys).arg(key).arg(dir).arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the up to `count` first elements of the list stored at key.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// If `count` is not specified, then defaults to first element.","highlight_start":1,"highlight_end":1},{"text":"    fn lpop<K: ToRedisArgs>(key: K, count: Option<core::num::NonZeroUsize>) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LPOP\").arg(key).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the index of the first matching value of the list stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn lpos<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, options: LposOptions) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LPOS\").arg(key).arg(value).arg(options)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert all the specified values at the head of the list stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn lpush<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LPUSH\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Inserts a value at the head of the list stored at key, only if key","highlight_start":1,"highlight_end":1},{"text":"    /// already exists and holds a list.","highlight_start":1,"highlight_end":1},{"text":"    fn lpush_exists<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LPUSHX\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the specified elements of the list stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn lrange<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LRANGE\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes the first count occurrences of elements equal to value","highlight_start":1,"highlight_end":1},{"text":"    /// from the list stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn lrem<K: ToRedisArgs, V: ToRedisArgs>(key: K, count: isize, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LREM\").arg(key).arg(count).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Trim an existing list so that it will contain only the specified","highlight_start":1,"highlight_end":1},{"text":"    /// range of elements specified.","highlight_start":1,"highlight_end":1},{"text":"    fn ltrim<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LTRIM\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets the list element at index to value","highlight_start":1,"highlight_end":1},{"text":"    fn lset<K: ToRedisArgs, V: ToRedisArgs>(key: K, index: isize, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"LSET\").arg(key).arg(index).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the up to `count` last elements of the list stored at key","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// If `count` is not specified, then defaults to last element.","highlight_start":1,"highlight_end":1},{"text":"    fn rpop<K: ToRedisArgs>(key: K, count: Option<core::num::NonZeroUsize>) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"RPOP\").arg(key).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop a value from a list, push it to another list and return it.","highlight_start":1,"highlight_end":1},{"text":"    fn rpoplpush<K: ToRedisArgs, D: ToRedisArgs>(key: K, dstkey: D) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"RPOPLPUSH\").arg(key).arg(dstkey)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert all the specified values at the tail of the list stored at key.","highlight_start":1,"highlight_end":1},{"text":"    fn rpush<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"RPUSH\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Inserts value at the tail of the list stored at key, only if key","highlight_start":1,"highlight_end":1},{"text":"    /// already exists and holds a list.","highlight_start":1,"highlight_end":1},{"text":"    fn rpush_exists<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"RPUSHX\").arg(key).arg(value)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // set commands","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add one or more members to a set.","highlight_start":1,"highlight_end":1},{"text":"    fn sadd<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SADD\").arg(key).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the number of members in a set.","highlight_start":1,"highlight_end":1},{"text":"    fn scard<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SCARD\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Subtract multiple sets.","highlight_start":1,"highlight_end":1},{"text":"    fn sdiff<K: ToRedisArgs>(keys: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SDIFF\").arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Subtract multiple sets and store the resulting set in a key.","highlight_start":1,"highlight_end":1},{"text":"    fn sdiffstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SDIFFSTORE\").arg(dstkey).arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sets.","highlight_start":1,"highlight_end":1},{"text":"    fn sinter<K: ToRedisArgs>(keys: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SINTER\").arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sets and store the resulting set in a key.","highlight_start":1,"highlight_end":1},{"text":"    fn sinterstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SINTERSTORE\").arg(dstkey).arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine if a given value is a member of a set.","highlight_start":1,"highlight_end":1},{"text":"    fn sismember<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SISMEMBER\").arg(key).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get all the members in a set.","highlight_start":1,"highlight_end":1},{"text":"    fn smembers<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SMEMBERS\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Move a member from one set to another.","highlight_start":1,"highlight_end":1},{"text":"    fn smove<S: ToRedisArgs, D: ToRedisArgs, M: ToRedisArgs>(srckey: S, dstkey: D, member: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SMOVE\").arg(srckey).arg(dstkey).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove and return a random member from a set.","highlight_start":1,"highlight_end":1},{"text":"    fn spop<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SPOP\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get one random member from a set.","highlight_start":1,"highlight_end":1},{"text":"    fn srandmember<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SRANDMEMBER\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get multiple random members from a set.","highlight_start":1,"highlight_end":1},{"text":"    fn srandmember_multiple<K: ToRedisArgs>(key: K, count: usize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SRANDMEMBER\").arg(key).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove one or more members from a set.","highlight_start":1,"highlight_end":1},{"text":"    fn srem<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SREM\").arg(key).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add multiple sets.","highlight_start":1,"highlight_end":1},{"text":"    fn sunion<K: ToRedisArgs>(keys: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SUNION\").arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add multiple sets and store the resulting set in a key.","highlight_start":1,"highlight_end":1},{"text":"    fn sunionstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"SUNIONSTORE\").arg(dstkey).arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // sorted set commands","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add one member to a sorted set, or update its score if it already exists.","highlight_start":1,"highlight_end":1},{"text":"    fn zadd<K: ToRedisArgs, S: ToRedisArgs, M: ToRedisArgs>(key: K, member: M, score: S) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZADD\").arg(key).arg(score).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add multiple members to a sorted set, or update its score if it already exists.","highlight_start":1,"highlight_end":1},{"text":"    fn zadd_multiple<K: ToRedisArgs, S: ToRedisArgs, M: ToRedisArgs>(key: K, items: &'a [(S, M)]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZADD\").arg(key).arg(items)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the number of members in a sorted set.","highlight_start":1,"highlight_end":1},{"text":"    fn zcard<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZCARD\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count the members in a sorted set with scores within the given values.","highlight_start":1,"highlight_end":1},{"text":"    fn zcount<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZCOUNT\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Increments the member in a sorted set at key by delta.","highlight_start":1,"highlight_end":1},{"text":"    /// If the member does not exist, it is added with delta as its score.","highlight_start":1,"highlight_end":1},{"text":"    fn zincr<K: ToRedisArgs, M: ToRedisArgs, D: ToRedisArgs>(key: K, member: M, delta: D) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZINCRBY\").arg(key).arg(delta).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":1},{"text":"    /// a new key using SUM as aggregation function.","highlight_start":1,"highlight_end":1},{"text":"    fn zinterstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":1},{"text":"    /// a new key using MIN as aggregation function.","highlight_start":1,"highlight_end":1},{"text":"    fn zinterstore_min<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":1},{"text":"    /// a new key using MAX as aggregation function.","highlight_start":1,"highlight_end":1},{"text":"    fn zinterstore_max<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zinterstore`], but with the ability to specify a","highlight_start":1,"highlight_end":1},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":1},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":1},{"text":"    fn zinterstore_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":1},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zinterstore_min`], but with the ability to specify a","highlight_start":1,"highlight_end":1},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":1},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":1},{"text":"    fn zinterstore_min_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":1},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zinterstore_max`], but with the ability to specify a","highlight_start":1,"highlight_end":1},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":1},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":1},{"text":"    fn zinterstore_max_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":1},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count the number of members in a sorted set between a given lexicographical range.","highlight_start":1,"highlight_end":1},{"text":"    fn zlexcount<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZLEXCOUNT\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the member with the highest score in a sorted set.","highlight_start":1,"highlight_end":1},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":1},{"text":"    fn bzpopmax<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BZPOPMAX\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the highest scores in a sorted set","highlight_start":1,"highlight_end":1},{"text":"    fn zpopmax<K: ToRedisArgs>(key: K, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZPOPMAX\").arg(key).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the member with the lowest score in a sorted set.","highlight_start":1,"highlight_end":1},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":1},{"text":"    fn bzpopmin<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BZPOPMIN\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the lowest scores in a sorted set","highlight_start":1,"highlight_end":1},{"text":"    fn zpopmin<K: ToRedisArgs>(key: K, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZPOPMIN\").arg(key).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the highest scores,","highlight_start":1,"highlight_end":1},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":1},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":1},{"text":"    fn bzmpop_max<K: ToRedisArgs>(timeout: f64, keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BZMPOP\").arg(timeout).arg(keys.len()).arg(keys).arg(\"MAX\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the highest scores,","highlight_start":1,"highlight_end":1},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":1},{"text":"    fn zmpop_max<K: ToRedisArgs>(keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZMPOP\").arg(keys.len()).arg(keys).arg(\"MAX\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the lowest scores,","highlight_start":1,"highlight_end":1},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":1},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":1},{"text":"    fn bzmpop_min<K: ToRedisArgs>(timeout: f64, keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"BZMPOP\").arg(timeout).arg(keys.len()).arg(keys).arg(\"MIN\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the lowest scores,","highlight_start":1,"highlight_end":1},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":1},{"text":"    fn zmpop_min<K: ToRedisArgs>(keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZMPOP\").arg(keys.len()).arg(keys).arg(\"MIN\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return up to count random members in a sorted set (or 1 if `count == None`)","highlight_start":1,"highlight_end":1},{"text":"    fn zrandmember<K: ToRedisArgs>(key: K, count: Option<isize>) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANDMEMBER\").arg(key).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return up to count random members in a sorted set with scores","highlight_start":1,"highlight_end":1},{"text":"    fn zrandmember_withscores<K: ToRedisArgs>(key: K, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANDMEMBER\").arg(key).arg(count).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index","highlight_start":1,"highlight_end":1},{"text":"    fn zrange<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGE\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index with scores.","highlight_start":1,"highlight_end":1},{"text":"    fn zrange_withscores<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGE\").arg(key).arg(start).arg(stop).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical range.","highlight_start":1,"highlight_end":1},{"text":"    fn zrangebylex<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGEBYLEX\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical","highlight_start":1,"highlight_end":1},{"text":"    /// range with offset and limit.","highlight_start":1,"highlight_end":1},{"text":"    fn zrangebylex_limit<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"            key: K, min: M, max: MM, offset: isize, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGEBYLEX\").arg(key).arg(min).arg(max).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical range.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrangebylex<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(key: K, max: MM, min: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGEBYLEX\").arg(key).arg(max).arg(min)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical","highlight_start":1,"highlight_end":1},{"text":"    /// range with offset and limit.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrangebylex_limit<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"            key: K, max: MM, min: M, offset: isize, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGEBYLEX\").arg(key).arg(max).arg(min).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score.","highlight_start":1,"highlight_end":1},{"text":"    fn zrangebyscore<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with scores.","highlight_start":1,"highlight_end":1},{"text":"    fn zrangebyscore_withscores<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit.","highlight_start":1,"highlight_end":1},{"text":"    fn zrangebyscore_limit<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>","highlight_start":1,"highlight_end":1},{"text":"            (key: K, min: M, max: MM, offset: isize, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit with scores.","highlight_start":1,"highlight_end":1},{"text":"    fn zrangebyscore_limit_withscores<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>","highlight_start":1,"highlight_end":1},{"text":"            (key: K, min: M, max: MM, offset: isize, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine the index of a member in a sorted set.","highlight_start":1,"highlight_end":1},{"text":"    fn zrank<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZRANK\").arg(key).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove one or more members from a sorted set.","highlight_start":1,"highlight_end":1},{"text":"    fn zrem<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREM\").arg(key).arg(members)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove all members in a sorted set between the given lexicographical range.","highlight_start":1,"highlight_end":1},{"text":"    fn zrembylex<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREMRANGEBYLEX\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove all members in a sorted set within the given indexes.","highlight_start":1,"highlight_end":1},{"text":"    fn zremrangebyrank<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREMRANGEBYRANK\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove all members in a sorted set within the given scores.","highlight_start":1,"highlight_end":1},{"text":"    fn zrembyscore<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREMRANGEBYSCORE\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index, with scores","highlight_start":1,"highlight_end":1},{"text":"    /// ordered from high to low.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrange<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGE\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index, with scores","highlight_start":1,"highlight_end":1},{"text":"    /// ordered from high to low.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrange_withscores<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGE\").arg(key).arg(start).arg(stop).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrangebyscore<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(key: K, max: MM, min: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with scores.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrangebyscore_withscores<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(key: K, max: MM, min: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrangebyscore_limit<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>","highlight_start":1,"highlight_end":1},{"text":"            (key: K, max: MM, min: M, offset: isize, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit with scores.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrangebyscore_limit_withscores<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>","highlight_start":1,"highlight_end":1},{"text":"            (key: K, max: MM, min: M, offset: isize, count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine the index of a member in a sorted set, with scores ordered from high to low.","highlight_start":1,"highlight_end":1},{"text":"    fn zrevrank<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZREVRANK\").arg(key).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the score associated with the given member in a sorted set.","highlight_start":1,"highlight_end":1},{"text":"    fn zscore<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZSCORE\").arg(key).arg(member)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the scores associated with multiple members in a sorted set.","highlight_start":1,"highlight_end":1},{"text":"    fn zscore_multiple<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: &'a [M]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZMSCORE\").arg(key).arg(members)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unions multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":1},{"text":"    /// a new key using SUM as aggregation function.","highlight_start":1,"highlight_end":1},{"text":"    fn zunionstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unions multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":1},{"text":"    /// a new key using MIN as aggregation function.","highlight_start":1,"highlight_end":1},{"text":"    fn zunionstore_min<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unions multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":1},{"text":"    /// a new key using MAX as aggregation function.","highlight_start":1,"highlight_end":1},{"text":"    fn zunionstore_max<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zunionstore`], but with the ability to specify a","highlight_start":1,"highlight_end":1},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":1},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":1},{"text":"    fn zunionstore_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":1},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zunionstore_min`], but with the ability to specify a","highlight_start":1,"highlight_end":1},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":1},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":1},{"text":"    fn zunionstore_min_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":1},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zunionstore_max`], but with the ability to specify a","highlight_start":1,"highlight_end":1},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":1},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":1},{"text":"    fn zunionstore_max_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":1},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // hyperloglog commands","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Adds the specified elements to the specified HyperLogLog.","highlight_start":1,"highlight_end":1},{"text":"    fn pfadd<K: ToRedisArgs, E: ToRedisArgs>(key: K, element: E) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PFADD\").arg(key).arg(element)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the approximated cardinality of the set(s) observed by the","highlight_start":1,"highlight_end":1},{"text":"    /// HyperLogLog at key(s).","highlight_start":1,"highlight_end":1},{"text":"    fn pfcount<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PFCOUNT\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Merge N different HyperLogLogs into a single one.","highlight_start":1,"highlight_end":1},{"text":"    fn pfmerge<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PFMERGE\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Posts a message to the given channel.","highlight_start":1,"highlight_end":1},{"text":"    fn publish<K: ToRedisArgs, E: ToRedisArgs>(channel: K, message: E) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"PUBLISH\").arg(channel).arg(message)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Object commands","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the encoding of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn object_encoding<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"OBJECT\").arg(\"ENCODING\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the time in seconds since the last access of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn object_idletime<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"OBJECT\").arg(\"IDLETIME\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the logarithmic access frequency counter of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn object_freq<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"OBJECT\").arg(\"FREQ\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the reference count of a key.","highlight_start":1,"highlight_end":1},{"text":"    fn object_refcount<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"OBJECT\").arg(\"REFCOUNT\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // ACL commands","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// When Redis is configured to use an ACL file (with the aclfile","highlight_start":1,"highlight_end":1},{"text":"    /// configuration option), this command will reload the ACLs from the file,","highlight_start":1,"highlight_end":1},{"text":"    /// replacing all the current ACL rules with the ones defined in the file.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_load<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"LOAD\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// When Redis is configured to use an ACL file (with the aclfile","highlight_start":1,"highlight_end":1},{"text":"    /// configuration option), this command will save the currently defined","highlight_start":1,"highlight_end":1},{"text":"    /// ACLs from the server memory to the ACL file.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_save<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"SAVE\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows the currently active ACL rules in the Redis server.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_list<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"LIST\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows a list of all the usernames of the currently configured users in","highlight_start":1,"highlight_end":1},{"text":"    /// the Redis ACL system.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_users<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"USERS\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns all the rules defined for an existing ACL user.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_getuser<K: ToRedisArgs>(username: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"GETUSER\").arg(username)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Creates an ACL user without any privilege.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_setuser<K: ToRedisArgs>(username: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"SETUSER\").arg(username)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Creates an ACL user with the specified rules or modify the rules of","highlight_start":1,"highlight_end":1},{"text":"    /// an existing user.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_setuser_rules<K: ToRedisArgs>(username: K, rules: &'a [acl::Rule]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"SETUSER\").arg(username).arg(rules)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Delete all the specified ACL users and terminate all the connections","highlight_start":1,"highlight_end":1},{"text":"    /// that are authenticated with such users.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_deluser<K: ToRedisArgs>(usernames: &'a [K]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"DELUSER\").arg(usernames)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows the available ACL categories.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_cat<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"CAT\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows all the Redis commands in the specified category.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_cat_categoryname<K: ToRedisArgs>(categoryname: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"CAT\").arg(categoryname)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Generates a 256-bits password starting from /dev/urandom if available.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_genpass<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"GENPASS\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Generates a 1-to-1024-bits password starting from /dev/urandom if available.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_genpass_bits<>(bits: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"GENPASS\").arg(bits)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the username the current connection is authenticated with.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_whoami<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"WHOAMI\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows a list of recent ACL security events","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_log<>(count: isize) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"LOG\").arg(count)","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Clears the ACL log.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_log_reset<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"LOG\").arg(\"RESET\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns a helpful text describing the different subcommands.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn acl_help<>() {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"ACL\").arg(\"HELP\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    //","highlight_start":1,"highlight_end":1},{"text":"    // geospatial commands","highlight_start":1,"highlight_end":1},{"text":"    //","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Adds the specified geospatial items to the specified key.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Every member has to be written as a tuple of `(longitude, latitude,","highlight_start":1,"highlight_end":1},{"text":"    /// member_name)`. It can be a single tuple, or a vector of tuples.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// `longitude, latitude` can be set using [`redis::geo::Coord`][1].","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// [1]: ./geo/struct.Coord.html","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the number of elements added to the sorted set, not including","highlight_start":1,"highlight_end":1},{"text":"    /// elements already existing for which the score was updated.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// # Example","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::{Commands, Connection, RedisResult};","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::geo::Coord;","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// fn add_point(con: &mut Connection) -> RedisResult<isize> {","highlight_start":1,"highlight_end":1},{"text":"    ///     con.geo_add(\"my_gis\", (Coord::lon_lat(13.361389, 38.115556), \"Palermo\"))","highlight_start":1,"highlight_end":1},{"text":"    /// }","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// fn add_point_with_tuples(con: &mut Connection) -> RedisResult<isize> {","highlight_start":1,"highlight_end":1},{"text":"    ///     con.geo_add(\"my_gis\", (\"13.361389\", \"38.115556\", \"Palermo\"))","highlight_start":1,"highlight_end":1},{"text":"    /// }","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// fn add_many_points(con: &mut Connection) -> RedisResult<isize> {","highlight_start":1,"highlight_end":1},{"text":"    ///     con.geo_add(\"my_gis\", &[","highlight_start":1,"highlight_end":1},{"text":"    ///         (\"13.361389\", \"38.115556\", \"Palermo\"),","highlight_start":1,"highlight_end":1},{"text":"    ///         (\"15.087269\", \"37.502669\", \"Catania\")","highlight_start":1,"highlight_end":1},{"text":"    ///     ])","highlight_start":1,"highlight_end":1},{"text":"    /// }","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn geo_add<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GEOADD\").arg(key).arg(members)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the distance between two members in the geospatial index","highlight_start":1,"highlight_end":1},{"text":"    /// represented by the sorted set.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// If one or both the members are missing, the command returns NULL, so","highlight_start":1,"highlight_end":1},{"text":"    /// it may be convenient to parse its response as either `Option<f64>` or","highlight_start":1,"highlight_end":1},{"text":"    /// `Option<String>`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// # Example","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::geo::Unit;","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// fn get_dists(con: &mut redis::Connection) {","highlight_start":1,"highlight_end":1},{"text":"    ///     let x: RedisResult<f64> = con.geo_dist(","highlight_start":1,"highlight_end":1},{"text":"    ///         \"my_gis\",","highlight_start":1,"highlight_end":1},{"text":"    ///         \"Palermo\",","highlight_start":1,"highlight_end":1},{"text":"    ///         \"Catania\",","highlight_start":1,"highlight_end":1},{"text":"    ///         Unit::Kilometers","highlight_start":1,"highlight_end":1},{"text":"    ///     );","highlight_start":1,"highlight_end":1},{"text":"    ///     // x is Ok(166.2742)","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    ///     let x: RedisResult<Option<f64>> = con.geo_dist(","highlight_start":1,"highlight_end":1},{"text":"    ///         \"my_gis\",","highlight_start":1,"highlight_end":1},{"text":"    ///         \"Palermo\",","highlight_start":1,"highlight_end":1},{"text":"    ///         \"Atlantis\",","highlight_start":1,"highlight_end":1},{"text":"    ///         Unit::Meters","highlight_start":1,"highlight_end":1},{"text":"    ///     );","highlight_start":1,"highlight_end":1},{"text":"    ///     // x is Ok(None)","highlight_start":1,"highlight_end":1},{"text":"    /// }","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn geo_dist<K: ToRedisArgs, M1: ToRedisArgs, M2: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        member1: M1,","highlight_start":1,"highlight_end":1},{"text":"        member2: M2,","highlight_start":1,"highlight_end":1},{"text":"        unit: geo::Unit","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GEODIST\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(member1)","highlight_start":1,"highlight_end":1},{"text":"            .arg(member2)","highlight_start":1,"highlight_end":1},{"text":"            .arg(unit)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return valid [Geohash][1] strings representing the position of one or","highlight_start":1,"highlight_end":1},{"text":"    /// more members of the geospatial index represented by the sorted set at","highlight_start":1,"highlight_end":1},{"text":"    /// key.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// [1]: https://en.wikipedia.org/wiki/Geohash","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// # Example","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// fn get_hash(con: &mut redis::Connection) {","highlight_start":1,"highlight_end":1},{"text":"    ///     let x: RedisResult<Vec<String>> = con.geo_hash(\"my_gis\", \"Palermo\");","highlight_start":1,"highlight_end":1},{"text":"    ///     // x is vec![\"sqc8b49rny0\"]","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    ///     let x: RedisResult<Vec<String>> = con.geo_hash(\"my_gis\", &[\"Palermo\", \"Catania\"]);","highlight_start":1,"highlight_end":1},{"text":"    ///     // x is vec![\"sqc8b49rny0\", \"sqdtr74hyu0\"]","highlight_start":1,"highlight_end":1},{"text":"    /// }","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn geo_hash<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GEOHASH\").arg(key).arg(members)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the positions of all the specified members of the geospatial","highlight_start":1,"highlight_end":1},{"text":"    /// index represented by the sorted set at key.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Every position is a pair of `(longitude, latitude)`. [`redis::geo::Coord`][1]","highlight_start":1,"highlight_end":1},{"text":"    /// can be used to convert these value in a struct.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// [1]: ./geo/struct.Coord.html","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// # Example","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::geo::Coord;","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// fn get_position(con: &mut redis::Connection) {","highlight_start":1,"highlight_end":1},{"text":"    ///     let x: RedisResult<Vec<Vec<f64>>> = con.geo_pos(\"my_gis\", &[\"Palermo\", \"Catania\"]);","highlight_start":1,"highlight_end":1},{"text":"    ///     // x is [ [ 13.361389, 38.115556 ], [ 15.087269, 37.502669 ] ];","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    ///     let x: Vec<Coord<f64>> = con.geo_pos(\"my_gis\", \"Palermo\").unwrap();","highlight_start":1,"highlight_end":1},{"text":"    ///     // x[0].longitude is 13.361389","highlight_start":1,"highlight_end":1},{"text":"    ///     // x[0].latitude is 38.115556","highlight_start":1,"highlight_end":1},{"text":"    /// }","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn geo_pos<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GEOPOS\").arg(key).arg(members)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the members of a sorted set populated with geospatial information","highlight_start":1,"highlight_end":1},{"text":"    /// using [`geo_add`](#method.geo_add), which are within the borders of the area","highlight_start":1,"highlight_end":1},{"text":"    /// specified with the center location and the maximum distance from the center","highlight_start":1,"highlight_end":1},{"text":"    /// (the radius).","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Every item in the result can be read with [`redis::geo::RadiusSearchResult`][1],","highlight_start":1,"highlight_end":1},{"text":"    /// which support the multiple formats returned by `GEORADIUS`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// [1]: ./geo/struct.RadiusSearchResult.html","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::geo::{RadiusOptions, RadiusSearchResult, RadiusOrder, Unit};","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// fn radius(con: &mut redis::Connection) -> Vec<RadiusSearchResult> {","highlight_start":1,"highlight_end":1},{"text":"    ///     let opts = RadiusOptions::default().with_dist().order(RadiusOrder::Asc);","highlight_start":1,"highlight_end":1},{"text":"    ///     con.geo_radius(\"my_gis\", 15.90, 37.21, 51.39, Unit::Kilometers, opts).unwrap()","highlight_start":1,"highlight_end":1},{"text":"    /// }","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn geo_radius<K: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        longitude: f64,","highlight_start":1,"highlight_end":1},{"text":"        latitude: f64,","highlight_start":1,"highlight_end":1},{"text":"        radius: f64,","highlight_start":1,"highlight_end":1},{"text":"        unit: geo::Unit,","highlight_start":1,"highlight_end":1},{"text":"        options: geo::RadiusOptions","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GEORADIUS\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(longitude)","highlight_start":1,"highlight_end":1},{"text":"            .arg(latitude)","highlight_start":1,"highlight_end":1},{"text":"            .arg(radius)","highlight_start":1,"highlight_end":1},{"text":"            .arg(unit)","highlight_start":1,"highlight_end":1},{"text":"            .arg(options)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Retrieve members selected by distance with the center of `member`. The","highlight_start":1,"highlight_end":1},{"text":"    /// member itself is always contained in the results.","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn geo_radius_by_member<K: ToRedisArgs, M: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        member: M,","highlight_start":1,"highlight_end":1},{"text":"        radius: f64,","highlight_start":1,"highlight_end":1},{"text":"        unit: geo::Unit,","highlight_start":1,"highlight_end":1},{"text":"        options: geo::RadiusOptions","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GEORADIUSBYMEMBER\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(member)","highlight_start":1,"highlight_end":1},{"text":"            .arg(radius)","highlight_start":1,"highlight_end":1},{"text":"            .arg(unit)","highlight_start":1,"highlight_end":1},{"text":"            .arg(options)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    //","highlight_start":1,"highlight_end":1},{"text":"    // streams commands","highlight_start":1,"highlight_end":1},{"text":"    //","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Ack pending stream messages checked out by a consumer.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XACK <key> <group> <id> <id> ... <id>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xack<K: ToRedisArgs, G: ToRedisArgs, I: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        ids: &'a [I]) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XACK\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(ids)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add a stream message by `key`. Use `*` as the `id` for the current timestamp.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XADD key <ID or *> [field value] [field value] ...","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xadd<K: ToRedisArgs, ID: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        id: ID,","highlight_start":1,"highlight_end":1},{"text":"        items: &'a [(F, V)]","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XADD\").arg(key).arg(id).arg(items)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// BTreeMap variant for adding a stream message by `key`.","highlight_start":1,"highlight_end":1},{"text":"    /// Use `*` as the `id` for the current timestamp.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XADD key <ID or *> [rust BTreeMap] ...","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xadd_map<K: ToRedisArgs, ID: ToRedisArgs, BTM: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        id: ID,","highlight_start":1,"highlight_end":1},{"text":"        map: BTM","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XADD\").arg(key).arg(id).arg(map)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add a stream message while capping the stream at a maxlength.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XADD key [MAXLEN [~|=] <count>] <ID or *> [field value] [field value] ...","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xadd_maxlen<","highlight_start":1,"highlight_end":1},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        ID: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        F: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        V: ToRedisArgs","highlight_start":1,"highlight_end":1},{"text":"    >(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        maxlen: streams::StreamMaxlen,","highlight_start":1,"highlight_end":1},{"text":"        id: ID,","highlight_start":1,"highlight_end":1},{"text":"        items: &'a [(F, V)]","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XADD\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(maxlen)","highlight_start":1,"highlight_end":1},{"text":"            .arg(id)","highlight_start":1,"highlight_end":1},{"text":"            .arg(items)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// BTreeMap variant for adding a stream message while capping the stream at a maxlength.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XADD key [MAXLEN [~|=] <count>] <ID or *> [rust BTreeMap] ...","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xadd_maxlen_map<K: ToRedisArgs, ID: ToRedisArgs, BTM: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        maxlen: streams::StreamMaxlen,","highlight_start":1,"highlight_end":1},{"text":"        id: ID,","highlight_start":1,"highlight_end":1},{"text":"        map: BTM","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XADD\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(maxlen)","highlight_start":1,"highlight_end":1},{"text":"            .arg(id)","highlight_start":1,"highlight_end":1},{"text":"            .arg(map)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Claim pending, unacked messages, after some period of time,","highlight_start":1,"highlight_end":1},{"text":"    /// currently checked out by another consumer.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// This method only accepts the must-have arguments for claiming messages.","highlight_start":1,"highlight_end":1},{"text":"    /// If optional arguments are required, see `xclaim_options` below.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XCLAIM <key> <group> <consumer> <min-idle-time> [<ID-1> <ID-2>]","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xclaim<K: ToRedisArgs, G: ToRedisArgs, C: ToRedisArgs, MIT: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        consumer: C,","highlight_start":1,"highlight_end":1},{"text":"        min_idle_time: MIT,","highlight_start":1,"highlight_end":1},{"text":"        ids: &'a [ID]","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XCLAIM\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":1},{"text":"            .arg(min_idle_time)","highlight_start":1,"highlight_end":1},{"text":"            .arg(ids)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the optional arguments version for claiming unacked, pending messages","highlight_start":1,"highlight_end":1},{"text":"    /// currently checked out by another consumer.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```no_run","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::{Connection,Commands,RedisResult};","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::streams::{StreamClaimOptions,StreamClaimReply};","highlight_start":1,"highlight_end":1},{"text":"    /// let client = redis::Client::open(\"redis://127.0.0.1/0\").unwrap();","highlight_start":1,"highlight_end":1},{"text":"    /// let mut con = client.get_connection().unwrap();","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// // Claim all pending messages for key \"k1\",","highlight_start":1,"highlight_end":1},{"text":"    /// // from group \"g1\", checked out by consumer \"c1\"","highlight_start":1,"highlight_end":1},{"text":"    /// // for 10ms with RETRYCOUNT 2 and FORCE","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// let opts = StreamClaimOptions::default()","highlight_start":1,"highlight_end":1},{"text":"    ///     .with_force()","highlight_start":1,"highlight_end":1},{"text":"    ///     .retry(2);","highlight_start":1,"highlight_end":1},{"text":"    /// let results: RedisResult<StreamClaimReply> =","highlight_start":1,"highlight_end":1},{"text":"    ///     con.xclaim_options(\"k1\", \"g1\", \"c1\", 10, &[\"0\"], opts);","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// // All optional arguments return a `Result<StreamClaimReply>` with one exception:","highlight_start":1,"highlight_end":1},{"text":"    /// // Passing JUSTID returns only the message `id` and omits the HashMap for each message.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// let opts = StreamClaimOptions::default()","highlight_start":1,"highlight_end":1},{"text":"    ///     .with_justid();","highlight_start":1,"highlight_end":1},{"text":"    /// let results: RedisResult<Vec<String>> =","highlight_start":1,"highlight_end":1},{"text":"    ///     con.xclaim_options(\"k1\", \"g1\", \"c1\", 10, &[\"0\"], opts);","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XCLAIM <key> <group> <consumer> <min-idle-time> <ID-1> <ID-2>","highlight_start":1,"highlight_end":1},{"text":"    ///     [IDLE <milliseconds>] [TIME <mstime>] [RETRYCOUNT <count>]","highlight_start":1,"highlight_end":1},{"text":"    ///     [FORCE] [JUSTID]","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xclaim_options<","highlight_start":1,"highlight_end":1},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        C: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        MIT: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        ID: ToRedisArgs","highlight_start":1,"highlight_end":1},{"text":"    >(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        consumer: C,","highlight_start":1,"highlight_end":1},{"text":"        min_idle_time: MIT,","highlight_start":1,"highlight_end":1},{"text":"        ids: &'a [ID],","highlight_start":1,"highlight_end":1},{"text":"        options: streams::StreamClaimOptions","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XCLAIM\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":1},{"text":"            .arg(min_idle_time)","highlight_start":1,"highlight_end":1},{"text":"            .arg(ids)","highlight_start":1,"highlight_end":1},{"text":"            .arg(options)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Deletes a list of `id`s for a given stream `key`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XDEL <key> [<ID1> <ID2> ... <IDN>]","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xdel<K: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        ids: &'a [ID]","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XDEL\").arg(key).arg(ids)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This command is used for creating a consumer `group`. It expects the stream key","highlight_start":1,"highlight_end":1},{"text":"    /// to already exist. Otherwise, use `xgroup_create_mkstream` if it doesn't.","highlight_start":1,"highlight_end":1},{"text":"    /// The `id` is the starting message id all consumers should read from. Use `$` If you want","highlight_start":1,"highlight_end":1},{"text":"    /// all consumers to read from the last message added to stream.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XGROUP CREATE <key> <groupname> <id or $>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xgroup_create<K: ToRedisArgs, G: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        id: ID","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"CREATE\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(id)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the alternate version for creating a consumer `group`","highlight_start":1,"highlight_end":1},{"text":"    /// which makes the stream if it doesn't exist.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XGROUP CREATE <key> <groupname> <id or $> [MKSTREAM]","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xgroup_create_mkstream<","highlight_start":1,"highlight_end":1},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        ID: ToRedisArgs","highlight_start":1,"highlight_end":1},{"text":"    >(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        id: ID","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"CREATE\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(id)","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"MKSTREAM\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Alter which `id` you want consumers to begin reading from an existing","highlight_start":1,"highlight_end":1},{"text":"    /// consumer `group`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XGROUP SETID <key> <groupname> <id or $>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xgroup_setid<K: ToRedisArgs, G: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        id: ID","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"SETID\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(id)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Destroy an existing consumer `group` for a given stream `key`","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XGROUP SETID <key> <groupname> <id or $>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xgroup_destroy<K: ToRedisArgs, G: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XGROUP\").arg(\"DESTROY\").arg(key).arg(group)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This deletes a `consumer` from an existing consumer `group`","highlight_start":1,"highlight_end":1},{"text":"    /// for given stream `key.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XGROUP DELCONSUMER <key> <groupname> <consumername>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xgroup_delconsumer<K: ToRedisArgs, G: ToRedisArgs, C: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        consumer: C","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"DELCONSUMER\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This returns all info details about","highlight_start":1,"highlight_end":1},{"text":"    /// which consumers have read messages for given consumer `group`.","highlight_start":1,"highlight_end":1},{"text":"    /// Take note of the StreamInfoConsumersReply return type.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// *It's possible this return value might not contain new fields","highlight_start":1,"highlight_end":1},{"text":"    /// added by Redis in future versions.*","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XINFO CONSUMERS <key> <group>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xinfo_consumers<K: ToRedisArgs, G: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XINFO\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"CONSUMERS\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns all consumer `group`s created for a given stream `key`.","highlight_start":1,"highlight_end":1},{"text":"    /// Take note of the StreamInfoGroupsReply return type.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// *It's possible this return value might not contain new fields","highlight_start":1,"highlight_end":1},{"text":"    /// added by Redis in future versions.*","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XINFO GROUPS <key>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xinfo_groups<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XINFO\").arg(\"GROUPS\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns info about high-level stream details","highlight_start":1,"highlight_end":1},{"text":"    /// (first & last message `id`, length, number of groups, etc.)","highlight_start":1,"highlight_end":1},{"text":"    /// Take note of the StreamInfoStreamReply return type.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// *It's possible this return value might not contain new fields","highlight_start":1,"highlight_end":1},{"text":"    /// added by Redis in future versions.*","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XINFO STREAM <key>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xinfo_stream<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XINFO\").arg(\"STREAM\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the number of messages for a given stream `key`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XLEN <key>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xlen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XLEN\").arg(key)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is a basic version of making XPENDING command calls which only","highlight_start":1,"highlight_end":1},{"text":"    /// passes a stream `key` and consumer `group` and it","highlight_start":1,"highlight_end":1},{"text":"    /// returns details about which consumers have pending messages","highlight_start":1,"highlight_end":1},{"text":"    /// that haven't been acked.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// You can use this method along with","highlight_start":1,"highlight_end":1},{"text":"    /// `xclaim` or `xclaim_options` for determining which messages","highlight_start":1,"highlight_end":1},{"text":"    /// need to be retried.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Take note of the StreamPendingReply return type.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XPENDING <key> <group> [<start> <stop> <count> [<consumer>]]","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xpending<K: ToRedisArgs, G: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G","highlight_start":1,"highlight_end":1},{"text":"    )  {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XPENDING\").arg(key).arg(group)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This XPENDING version returns a list of all messages over the range.","highlight_start":1,"highlight_end":1},{"text":"    /// You can use this for paginating pending messages (but without the message HashMap).","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Start and end follow the same rules `xrange` args. Set start to `-`","highlight_start":1,"highlight_end":1},{"text":"    /// and end to `+` for the entire stream.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Take note of the StreamPendingCountReply return type.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XPENDING <key> <group> <start> <stop> <count>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xpending_count<","highlight_start":1,"highlight_end":1},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        S: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        E: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        C: ToRedisArgs","highlight_start":1,"highlight_end":1},{"text":"    >(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        start: S,","highlight_start":1,"highlight_end":1},{"text":"        end: E,","highlight_start":1,"highlight_end":1},{"text":"        count: C","highlight_start":1,"highlight_end":1},{"text":"    )  {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XPENDING\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(start)","highlight_start":1,"highlight_end":1},{"text":"            .arg(end)","highlight_start":1,"highlight_end":1},{"text":"            .arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// An alternate version of `xpending_count` which filters by `consumer` name.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Start and end follow the same rules `xrange` args. Set start to `-`","highlight_start":1,"highlight_end":1},{"text":"    /// and end to `+` for the entire stream.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Take note of the StreamPendingCountReply return type.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XPENDING <key> <group> <start> <stop> <count> <consumer>","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xpending_consumer_count<","highlight_start":1,"highlight_end":1},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        S: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        E: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        C: ToRedisArgs,","highlight_start":1,"highlight_end":1},{"text":"        CN: ToRedisArgs","highlight_start":1,"highlight_end":1},{"text":"    >(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        group: G,","highlight_start":1,"highlight_end":1},{"text":"        start: S,","highlight_start":1,"highlight_end":1},{"text":"        end: E,","highlight_start":1,"highlight_end":1},{"text":"        count: C,","highlight_start":1,"highlight_end":1},{"text":"        consumer: CN","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XPENDING\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(group)","highlight_start":1,"highlight_end":1},{"text":"            .arg(start)","highlight_start":1,"highlight_end":1},{"text":"            .arg(end)","highlight_start":1,"highlight_end":1},{"text":"            .arg(count)","highlight_start":1,"highlight_end":1},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns a range of messages in a given stream `key`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Set `start` to `-` to begin at the first message.","highlight_start":1,"highlight_end":1},{"text":"    /// Set `end` to `+` to end the most recent message.","highlight_start":1,"highlight_end":1},{"text":"    /// You can pass message `id` to both `start` and `end`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// Take note of the StreamRangeReply return type.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XRANGE key start end","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xrange<K: ToRedisArgs, S: ToRedisArgs, E: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        start: S,","highlight_start":1,"highlight_end":1},{"text":"        end: E","highlight_start":1,"highlight_end":1},{"text":"    )  {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XRANGE\").arg(key).arg(start).arg(end)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// A helper method for automatically returning all messages in a stream by `key`.","highlight_start":1,"highlight_end":1},{"text":"    /// **Use with caution!**","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XRANGE key - +","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xrange_all<K: ToRedisArgs>(key: K)  {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XRANGE\").arg(key).arg(\"-\").arg(\"+\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// A method for paginating a stream by `key`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XRANGE key start end [COUNT <n>]","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xrange_count<K: ToRedisArgs, S: ToRedisArgs, E: ToRedisArgs, C: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        start: S,","highlight_start":1,"highlight_end":1},{"text":"        end: E,","highlight_start":1,"highlight_end":1},{"text":"        count: C","highlight_start":1,"highlight_end":1},{"text":"    )  {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XRANGE\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(start)","highlight_start":1,"highlight_end":1},{"text":"            .arg(end)","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"COUNT\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Read a list of `id`s for each stream `key`.","highlight_start":1,"highlight_end":1},{"text":"    /// This is the basic form of reading streams.","highlight_start":1,"highlight_end":1},{"text":"    /// For more advanced control, like blocking, limiting, or reading by consumer `group`,","highlight_start":1,"highlight_end":1},{"text":"    /// see `xread_options`.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XREAD STREAMS key_1 key_2 ... key_N ID_1 ID_2 ... ID_N","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xread<K: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        keys: &'a [K],","highlight_start":1,"highlight_end":1},{"text":"        ids: &'a [ID]","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XREAD\").arg(\"STREAMS\").arg(keys).arg(ids)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This method handles setting optional arguments for","highlight_start":1,"highlight_end":1},{"text":"    /// `XREAD` or `XREADGROUP` Redis commands.","highlight_start":1,"highlight_end":1},{"text":"    /// ```no_run","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::{Connection,RedisResult,Commands};","highlight_start":1,"highlight_end":1},{"text":"    /// use redis::streams::{StreamReadOptions,StreamReadReply};","highlight_start":1,"highlight_end":1},{"text":"    /// let client = redis::Client::open(\"redis://127.0.0.1/0\").unwrap();","highlight_start":1,"highlight_end":1},{"text":"    /// let mut con = client.get_connection().unwrap();","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// // Read 10 messages from the start of the stream,","highlight_start":1,"highlight_end":1},{"text":"    /// // without registering as a consumer group.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// let opts = StreamReadOptions::default()","highlight_start":1,"highlight_end":1},{"text":"    ///     .count(10);","highlight_start":1,"highlight_end":1},{"text":"    /// let results: RedisResult<StreamReadReply> =","highlight_start":1,"highlight_end":1},{"text":"    ///     con.xread_options(&[\"k1\"], &[\"0\"], &opts);","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// // Read all undelivered messages for a given","highlight_start":1,"highlight_end":1},{"text":"    /// // consumer group. Be advised: the consumer group must already","highlight_start":1,"highlight_end":1},{"text":"    /// // exist before making this call. Also note: we're passing","highlight_start":1,"highlight_end":1},{"text":"    /// // '>' as the id here, which means all undelivered messages.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// let opts = StreamReadOptions::default()","highlight_start":1,"highlight_end":1},{"text":"    ///     .group(\"group-1\", \"consumer-1\");","highlight_start":1,"highlight_end":1},{"text":"    /// let results: RedisResult<StreamReadReply> =","highlight_start":1,"highlight_end":1},{"text":"    ///     con.xread_options(&[\"k1\"], &[\">\"], &opts);","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XREAD [BLOCK <milliseconds>] [COUNT <count>]","highlight_start":1,"highlight_end":1},{"text":"    ///     STREAMS key_1 key_2 ... key_N","highlight_start":1,"highlight_end":1},{"text":"    ///     ID_1 ID_2 ... ID_N","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// XREADGROUP [GROUP group-name consumer-name] [BLOCK <milliseconds>] [COUNT <count>] [NOACK] ","highlight_start":1,"highlight_end":1},{"text":"    ///     STREAMS key_1 key_2 ... key_N","highlight_start":1,"highlight_end":1},{"text":"    ///     ID_1 ID_2 ... ID_N","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xread_options<K: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        keys: &'a [K],","highlight_start":1,"highlight_end":1},{"text":"        ids: &'a [ID],","highlight_start":1,"highlight_end":1},{"text":"        options: &'a streams::StreamReadOptions","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(if options.read_only() {","highlight_start":1,"highlight_end":1},{"text":"            \"XREAD\"","highlight_start":1,"highlight_end":1},{"text":"        } else {","highlight_start":1,"highlight_end":1},{"text":"            \"XREADGROUP\"","highlight_start":1,"highlight_end":1},{"text":"        })","highlight_start":1,"highlight_end":1},{"text":"        .arg(options)","highlight_start":1,"highlight_end":1},{"text":"        .arg(\"STREAMS\")","highlight_start":1,"highlight_end":1},{"text":"        .arg(keys)","highlight_start":1,"highlight_end":1},{"text":"        .arg(ids)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the reverse version of `xrange`.","highlight_start":1,"highlight_end":1},{"text":"    /// The same rules apply for `start` and `end` here.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XREVRANGE key end start","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xrevrange<K: ToRedisArgs, E: ToRedisArgs, S: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        end: E,","highlight_start":1,"highlight_end":1},{"text":"        start: S","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XREVRANGE\").arg(key).arg(end).arg(start)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the reverse version of `xrange_all`.","highlight_start":1,"highlight_end":1},{"text":"    /// The same rules apply for `start` and `end` here.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XREVRANGE key + -","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    fn xrevrange_all<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XREVRANGE\").arg(key).arg(\"+\").arg(\"-\")","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the reverse version of `xrange_count`.","highlight_start":1,"highlight_end":1},{"text":"    /// The same rules apply for `start` and `end` here.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XREVRANGE key end start [COUNT <n>]","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xrevrange_count<K: ToRedisArgs, E: ToRedisArgs, S: ToRedisArgs, C: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        end: E,","highlight_start":1,"highlight_end":1},{"text":"        start: S,","highlight_start":1,"highlight_end":1},{"text":"        count: C","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XREVRANGE\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(key)","highlight_start":1,"highlight_end":1},{"text":"            .arg(end)","highlight_start":1,"highlight_end":1},{"text":"            .arg(start)","highlight_start":1,"highlight_end":1},{"text":"            .arg(\"COUNT\")","highlight_start":1,"highlight_end":1},{"text":"            .arg(count)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Trim a stream `key` to a MAXLEN count.","highlight_start":1,"highlight_end":1},{"text":"    ///","highlight_start":1,"highlight_end":1},{"text":"    /// ```text","highlight_start":1,"highlight_end":1},{"text":"    /// XTRIM <key> MAXLEN [~|=] <count>  (Same as XADD MAXLEN option)","highlight_start":1,"highlight_end":1},{"text":"    /// ```","highlight_start":1,"highlight_end":1},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":1},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":1},{"text":"    fn xtrim<K: ToRedisArgs>(","highlight_start":1,"highlight_end":1},{"text":"        key: K,","highlight_start":1,"highlight_end":1},{"text":"        maxlen: streams::StreamMaxlen","highlight_start":1,"highlight_end":1},{"text":"    ) {","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"XTRIM\").arg(key).arg(maxlen)","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":1},{"text":"}","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"implement_commands!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\macros.rs","byte_start":0,"byte_end":31,"line_start":1,"line_end":1,"column_start":1,"column_end":32,"is_primary":false,"text":[{"text":"macro_rules! implement_commands {","highlight_start":1,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `ping` found for struct `redis::aio::Connection` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:24:30\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let _: String = conn.ping().await?;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `xpending` with a similar name, but with different arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\mod.rs:41:1\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mimplement_commands! {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    'a\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1873\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `implement_commands` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\utils.rs","byte_start":2220,"byte_end":2247,"line_start":64,"line_end":64,"column_start":27,"column_end":54,"is_primary":true,"text":[{"text":"        conn.expire(&key, self.ttl.as_secs() as usize).await?;","highlight_start":27,"highlight_end":54}],"label":"expected `i64`, found `usize`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\utils.rs","byte_start":2207,"byte_end":2213,"line_start":64,"line_end":64,"column_start":14,"column_end":20,"is_primary":false,"text":[{"text":"        conn.expire(&key, self.ttl.as_secs() as usize).await?;","highlight_start":14,"highlight_end":20}],"label":"arguments to this method are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\mod.rs","byte_start":5192,"byte_end":5198,"line_start":133,"line_end":133,"column_start":8,"column_end":14,"is_primary":true,"text":[{"text":"    fn expire<K: ToRedisArgs>(key: K, seconds: i64) {","highlight_start":8,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can convert a `usize` to an `i64` and panic if the converted value doesn't fit","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":2220,"byte_end":2220,"line_start":64,"line_end":64,"column_start":27,"column_end":27,"is_primary":true,"text":[{"text":"        conn.expire(&key, self.ttl.as_secs() as usize).await?;","highlight_start":27,"highlight_end":27}],"label":null,"suggested_replacement":"(","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils.rs","byte_start":2247,"byte_end":2247,"line_start":64,"line_end":64,"column_start":54,"column_end":54,"is_primary":true,"text":[{"text":"        conn.expire(&key, self.ttl.as_secs() as usize).await?;","highlight_start":54,"highlight_end":54}],"label":null,"suggested_replacement":").try_into().unwrap()","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:64:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        conn.expire(&key, self.ttl.as_secs() as usize).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `i64`, found `usize`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14marguments to this method are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\mod.rs:133:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn expire<K: ToRedisArgs>(key: K, seconds: i64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can convert a `usize` to an `i64` and panic if the converted value doesn't fit\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        conn.expire(&key, \u001b[0m\u001b[0m\u001b[38;5;10m(\u001b[0m\u001b[0mself.ttl.as_secs() as usize\u001b[0m\u001b[0m\u001b[38;5;10m).try_into().unwrap()\u001b[0m\u001b[0m).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function depends on never type fallback being `()`","code":{"code":"dependency_on_unit_never_type_fallback","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":1458,"byte_end":1603,"line_start":46,"line_end":50,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    pub async fn acquire_processing_lock(","highlight_start":5,"highlight_end":42},{"text":"        &self, ","highlight_start":1,"highlight_end":16},{"text":"        record_id: &str, ","highlight_start":1,"highlight_end":26},{"text":"        retry_count: u32","highlight_start":1,"highlight_end":25},{"text":"    ) -> CallbackResult<ProcessingGuard> {","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!","code":null,"level":"warning","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"specify the types explicitly","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in edition 2024, the requirement `!: FromRedisValue` will fail","code":null,"level":"note","spans":[{"file_name":"src\\utils.rs","byte_start":2207,"byte_end":2213,"line_start":64,"line_end":64,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"        conn.expire(&key, self.ttl.as_secs() as usize).await?;","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`#[warn(dependency_on_unit_never_type_fallback)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `()` annotations to avoid fallback changes","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":2213,"byte_end":2213,"line_start":64,"line_end":64,"column_start":20,"column_end":20,"is_primary":true,"text":[{"text":"        conn.expire(&key, self.ttl.as_secs() as usize).await?;","highlight_start":20,"highlight_end":20}],"label":null,"suggested_replacement":"::<_, ()>","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:46:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn acquire_processing_lock(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self, \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        record_id: &str, \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        retry_count: u32\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> CallbackResult<ProcessingGuard> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|________________________________________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: FromRedisValue` will fail\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:64:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        conn.expire(&key, self.ttl.as_secs() as usize).await?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dependency_on_unit_never_type_fallback)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        conn.expire\u001b[0m\u001b[0m\u001b[38;5;10m::<_, ()>\u001b[0m\u001b[0m(&key, self.ttl.as_secs() as usize).await?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function depends on never type fallback being `()`","code":{"code":"dependency_on_unit_never_type_fallback","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils.rs","byte_start":3016,"byte_end":3147,"line_start":91,"line_end":95,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"    pub async fn mark_notification_sent(","highlight_start":5,"highlight_end":41},{"text":"        &self, ","highlight_start":1,"highlight_end":16},{"text":"        record_id: &str, ","highlight_start":1,"highlight_end":26},{"text":"        error_hash: &str","highlight_start":1,"highlight_end":25},{"text":"    ) -> CallbackResult<()> {","highlight_start":1,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!","code":null,"level":"warning","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"specify the types explicitly","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in edition 2024, the requirement `!: FromRedisValue` will fail","code":null,"level":"note","spans":[{"file_name":"src\\utils.rs","byte_start":3355,"byte_end":3361,"line_start":100,"line_end":100,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"        conn.set_ex(&key, \"1\", 600).await?;","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"use `()` annotations to avoid fallback changes","code":null,"level":"help","spans":[{"file_name":"src\\utils.rs","byte_start":3361,"byte_end":3361,"line_start":100,"line_end":100,"column_start":20,"column_end":20,"is_primary":true,"text":[{"text":"        conn.set_ex(&key, \"1\", 600).await?;","highlight_start":20,"highlight_end":20}],"label":null,"suggested_replacement":"::<_, _, ()>","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:91:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m91\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn mark_notification_sent(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self, \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m93\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        record_id: &str, \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        error_hash: &str\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> CallbackResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|___________________________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: FromRedisValue` will fail\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:100:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        conn.set_ex(&key, \"1\", 600).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        conn.set_ex\u001b[0m\u001b[0m\u001b[38;5;10m::<_, _, ()>\u001b[0m\u001b[0m(&key, \"1\", 600).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `ping` found for struct `redis::aio::Connection` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils.rs","byte_start":4516,"byte_end":4520,"line_start":136,"line_end":136,"column_start":51,"column_end":55,"is_primary":true,"text":[{"text":"                let result: Result<String, _> = c.ping().await;","highlight_start":51,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `xpending` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\macros.rs","byte_start":6752,"byte_end":7021,"line_start":162,"line_end":167,"column_start":17,"column_end":40,"is_primary":true,"text":[{"text":"                fn $name<$lifetime, $($tyargs: $ty + Send + Sync + $lifetime,)* RV>(","highlight_start":17,"highlight_end":85},{"text":"                    & $lifetime mut self","highlight_start":1,"highlight_end":41},{"text":"                    $(, $argname: $argty)*","highlight_start":1,"highlight_end":43},{"text":"                ) -> crate::types::RedisFuture<'a, RV>","highlight_start":1,"highlight_end":55},{"text":"                where","highlight_start":1,"highlight_end":22},{"text":"                    RV: FromRedisValue,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\mod.rs","byte_start":2151,"byte_end":65818,"line_start":41,"line_end":1873,"column_start":1,"column_end":2,"is_primary":false,"text":[{"text":"implement_commands! {","highlight_start":1,"highlight_end":22},{"text":"    'a","highlight_start":1,"highlight_end":7},{"text":"    // most common operations","highlight_start":1,"highlight_end":30},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the value of a key.  If key is a vec this becomes an `MGET`.","highlight_start":1,"highlight_end":73},{"text":"    fn get<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":37},{"text":"        cmd(if key.is_single_arg() { \"GET\" } else { \"MGET\" }).arg(key)","highlight_start":1,"highlight_end":71},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get values of keys","highlight_start":1,"highlight_end":27},{"text":"    fn mget<K: ToRedisArgs>(key: K){","highlight_start":1,"highlight_end":37},{"text":"        cmd(\"MGET\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all keys matching pattern","highlight_start":1,"highlight_end":39},{"text":"    fn keys<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":38},{"text":"        cmd(\"KEYS\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the string value of a key.","highlight_start":1,"highlight_end":39},{"text":"    fn set<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":63},{"text":"        cmd(\"SET\").arg(key).arg(value)","highlight_start":1,"highlight_end":39},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the string value of a key with options.","highlight_start":1,"highlight_end":52},{"text":"    fn set_options<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, options: SetOptions) {","highlight_start":1,"highlight_end":92},{"text":"        cmd(\"SET\").arg(key).arg(value).arg(options)","highlight_start":1,"highlight_end":52},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets multiple keys to their values.","highlight_start":1,"highlight_end":44},{"text":"    #[allow(deprecated)]","highlight_start":1,"highlight_end":25},{"text":"    #[deprecated(since = \"0.22.4\", note = \"Renamed to mset() to reflect Redis name\")]","highlight_start":1,"highlight_end":86},{"text":"    fn set_multiple<K: ToRedisArgs, V: ToRedisArgs>(items: &'a [(K, V)]) {","highlight_start":1,"highlight_end":75},{"text":"        cmd(\"MSET\").arg(items)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets multiple keys to their values.","highlight_start":1,"highlight_end":44},{"text":"    fn mset<K: ToRedisArgs, V: ToRedisArgs>(items: &'a [(K, V)]) {","highlight_start":1,"highlight_end":67},{"text":"        cmd(\"MSET\").arg(items)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the value and expiration of a key.","highlight_start":1,"highlight_end":47},{"text":"    fn set_ex<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, seconds: u64) {","highlight_start":1,"highlight_end":80},{"text":"        cmd(\"SETEX\").arg(key).arg(seconds).arg(value)","highlight_start":1,"highlight_end":54},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the value and expiration in milliseconds of a key.","highlight_start":1,"highlight_end":63},{"text":"    fn pset_ex<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, milliseconds: u64) {","highlight_start":1,"highlight_end":86},{"text":"        cmd(\"PSETEX\").arg(key).arg(milliseconds).arg(value)","highlight_start":1,"highlight_end":60},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the value of a key, only if the key does not exist","highlight_start":1,"highlight_end":63},{"text":"    fn set_nx<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":66},{"text":"        cmd(\"SETNX\").arg(key).arg(value)","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets multiple keys to their values failing if at least one already exists.","highlight_start":1,"highlight_end":83},{"text":"    fn mset_nx<K: ToRedisArgs, V: ToRedisArgs>(items: &'a [(K, V)]) {","highlight_start":1,"highlight_end":70},{"text":"        cmd(\"MSETNX\").arg(items)","highlight_start":1,"highlight_end":33},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the string value of a key and return its old value.","highlight_start":1,"highlight_end":64},{"text":"    fn getset<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":66},{"text":"        cmd(\"GETSET\").arg(key).arg(value)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get a range of bytes/substring from the value of a key. Negative values provide an offset from the end of the value.","highlight_start":1,"highlight_end":125},{"text":"    fn getrange<K: ToRedisArgs>(key: K, from: isize, to: isize) {","highlight_start":1,"highlight_end":66},{"text":"        cmd(\"GETRANGE\").arg(key).arg(from).arg(to)","highlight_start":1,"highlight_end":51},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Overwrite the part of the value stored in key at the specified offset.","highlight_start":1,"highlight_end":79},{"text":"    fn setrange<K: ToRedisArgs, V: ToRedisArgs>(key: K, offset: isize, value: V) {","highlight_start":1,"highlight_end":83},{"text":"        cmd(\"SETRANGE\").arg(key).arg(offset).arg(value)","highlight_start":1,"highlight_end":56},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Delete one or more keys.","highlight_start":1,"highlight_end":33},{"text":"    fn del<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":37},{"text":"        cmd(\"DEL\").arg(key)","highlight_start":1,"highlight_end":28},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine if a key exists.","highlight_start":1,"highlight_end":35},{"text":"    fn exists<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":40},{"text":"        cmd(\"EXISTS\").arg(key)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine the type of a key.","highlight_start":1,"highlight_end":37},{"text":"    fn key_type<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":42},{"text":"        cmd(\"TYPE\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set a key's time to live in seconds.","highlight_start":1,"highlight_end":45},{"text":"    fn expire<K: ToRedisArgs>(key: K, seconds: i64) {","highlight_start":1,"highlight_end":54},{"text":"        cmd(\"EXPIRE\").arg(key).arg(seconds)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the expiration for a key as a UNIX timestamp.","highlight_start":1,"highlight_end":58},{"text":"    fn expire_at<K: ToRedisArgs>(key: K, ts: i64) {","highlight_start":1,"highlight_end":52},{"text":"        cmd(\"EXPIREAT\").arg(key).arg(ts)","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set a key's time to live in milliseconds.","highlight_start":1,"highlight_end":50},{"text":"    fn pexpire<K: ToRedisArgs>(key: K, ms: i64) {","highlight_start":1,"highlight_end":50},{"text":"        cmd(\"PEXPIRE\").arg(key).arg(ms)","highlight_start":1,"highlight_end":40},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Set the expiration for a key as a UNIX timestamp in milliseconds.","highlight_start":1,"highlight_end":74},{"text":"    fn pexpire_at<K: ToRedisArgs>(key: K, ts: i64) {","highlight_start":1,"highlight_end":53},{"text":"        cmd(\"PEXPIREAT\").arg(key).arg(ts)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove the expiration from a key.","highlight_start":1,"highlight_end":42},{"text":"    fn persist<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":41},{"text":"        cmd(\"PERSIST\").arg(key)","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the expiration time of a key.","highlight_start":1,"highlight_end":42},{"text":"    fn ttl<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":37},{"text":"        cmd(\"TTL\").arg(key)","highlight_start":1,"highlight_end":28},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the expiration time of a key in milliseconds.","highlight_start":1,"highlight_end":58},{"text":"    fn pttl<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":38},{"text":"        cmd(\"PTTL\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the value of a key and set expiration","highlight_start":1,"highlight_end":50},{"text":"    fn get_ex<K: ToRedisArgs>(key: K, expire_at: Expiry) {","highlight_start":1,"highlight_end":59},{"text":"        let (option, time_arg) = match expire_at {","highlight_start":1,"highlight_end":51},{"text":"            Expiry::EX(sec) => (\"EX\", Some(sec)),","highlight_start":1,"highlight_end":50},{"text":"            Expiry::PX(ms) => (\"PX\", Some(ms)),","highlight_start":1,"highlight_end":48},{"text":"            Expiry::EXAT(timestamp_sec) => (\"EXAT\", Some(timestamp_sec)),","highlight_start":1,"highlight_end":74},{"text":"            Expiry::PXAT(timestamp_ms) => (\"PXAT\", Some(timestamp_ms)),","highlight_start":1,"highlight_end":72},{"text":"            Expiry::PERSIST => (\"PERSIST\", None),","highlight_start":1,"highlight_end":50},{"text":"        };","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        cmd(\"GETEX\").arg(key).arg(option).arg(time_arg)","highlight_start":1,"highlight_end":56},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the value of a key and delete it","highlight_start":1,"highlight_end":45},{"text":"    fn get_del<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":41},{"text":"        cmd(\"GETDEL\").arg(key)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Rename a key.","highlight_start":1,"highlight_end":22},{"text":"    fn rename<K: ToRedisArgs, N: ToRedisArgs>(key: K, new_key: N) {","highlight_start":1,"highlight_end":68},{"text":"        cmd(\"RENAME\").arg(key).arg(new_key)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Rename a key, only if the new key does not exist.","highlight_start":1,"highlight_end":58},{"text":"    fn rename_nx<K: ToRedisArgs, N: ToRedisArgs>(key: K, new_key: N) {","highlight_start":1,"highlight_end":71},{"text":"        cmd(\"RENAMENX\").arg(key).arg(new_key)","highlight_start":1,"highlight_end":46},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unlink one or more keys.","highlight_start":1,"highlight_end":33},{"text":"    fn unlink<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":40},{"text":"        cmd(\"UNLINK\").arg(key)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // common string operations","highlight_start":1,"highlight_end":32},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Append a value to a key.","highlight_start":1,"highlight_end":33},{"text":"    fn append<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":66},{"text":"        cmd(\"APPEND\").arg(key).arg(value)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Increment the numeric value of a key by the given amount.  This","highlight_start":1,"highlight_end":72},{"text":"    /// issues a `INCRBY` or `INCRBYFLOAT` depending on the type.","highlight_start":1,"highlight_end":66},{"text":"    fn incr<K: ToRedisArgs, V: ToRedisArgs>(key: K, delta: V) {","highlight_start":1,"highlight_end":64},{"text":"        cmd(if delta.describe_numeric_behavior() == NumericBehavior::NumberIsFloat {","highlight_start":1,"highlight_end":85},{"text":"            \"INCRBYFLOAT\"","highlight_start":1,"highlight_end":26},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            \"INCRBY\"","highlight_start":1,"highlight_end":21},{"text":"        }).arg(key).arg(delta)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Decrement the numeric value of a key by the given amount.","highlight_start":1,"highlight_end":66},{"text":"    fn decr<K: ToRedisArgs, V: ToRedisArgs>(key: K, delta: V) {","highlight_start":1,"highlight_end":64},{"text":"        cmd(\"DECRBY\").arg(key).arg(delta)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets or clears the bit at offset in the string value stored at key.","highlight_start":1,"highlight_end":76},{"text":"    fn setbit<K: ToRedisArgs>(key: K, offset: usize, value: bool) {","highlight_start":1,"highlight_end":68},{"text":"        cmd(\"SETBIT\").arg(key).arg(offset).arg(i32::from(value))","highlight_start":1,"highlight_end":65},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the bit value at offset in the string value stored at key.","highlight_start":1,"highlight_end":75},{"text":"    fn getbit<K: ToRedisArgs>(key: K, offset: usize) {","highlight_start":1,"highlight_end":55},{"text":"        cmd(\"GETBIT\").arg(key).arg(offset)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count set bits in a string.","highlight_start":1,"highlight_end":36},{"text":"    fn bitcount<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":42},{"text":"        cmd(\"BITCOUNT\").arg(key)","highlight_start":1,"highlight_end":33},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count set bits in a string in a range.","highlight_start":1,"highlight_end":47},{"text":"    fn bitcount_range<K: ToRedisArgs>(key: K, start: usize, end: usize) {","highlight_start":1,"highlight_end":74},{"text":"        cmd(\"BITCOUNT\").arg(key).arg(start).arg(end)","highlight_start":1,"highlight_end":53},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise AND between multiple keys (containing string values)","highlight_start":1,"highlight_end":79},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":53},{"text":"    fn bit_and<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":72},{"text":"        cmd(\"BITOP\").arg(\"AND\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":57},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise OR between multiple keys (containing string values)","highlight_start":1,"highlight_end":78},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":53},{"text":"    fn bit_or<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":71},{"text":"        cmd(\"BITOP\").arg(\"OR\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":56},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise XOR between multiple keys (containing string values)","highlight_start":1,"highlight_end":79},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":53},{"text":"    fn bit_xor<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":72},{"text":"        cmd(\"BITOP\").arg(\"XOR\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":57},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Perform a bitwise NOT of the key (containing string values)","highlight_start":1,"highlight_end":68},{"text":"    /// and store the result in the destination key.","highlight_start":1,"highlight_end":53},{"text":"    fn bit_not<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckey: S) {","highlight_start":1,"highlight_end":71},{"text":"        cmd(\"BITOP\").arg(\"NOT\").arg(dstkey).arg(srckey)","highlight_start":1,"highlight_end":56},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the length of the value stored in a key.","highlight_start":1,"highlight_end":53},{"text":"    fn strlen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":40},{"text":"        cmd(\"STRLEN\").arg(key)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // hash operations","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets a single (or multiple) fields from a hash.","highlight_start":1,"highlight_end":56},{"text":"    fn hget<K: ToRedisArgs, F: ToRedisArgs>(key: K, field: F) {","highlight_start":1,"highlight_end":64},{"text":"        cmd(if field.is_single_arg() { \"HGET\" } else { \"HMGET\" }).arg(key).arg(field)","highlight_start":1,"highlight_end":86},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Deletes a single (or multiple) fields from a hash.","highlight_start":1,"highlight_end":59},{"text":"    fn hdel<K: ToRedisArgs, F: ToRedisArgs>(key: K, field: F) {","highlight_start":1,"highlight_end":64},{"text":"        cmd(\"HDEL\").arg(key).arg(field)","highlight_start":1,"highlight_end":40},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets a single field in a hash.","highlight_start":1,"highlight_end":39},{"text":"    fn hset<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(key: K, field: F, value: V) {","highlight_start":1,"highlight_end":90},{"text":"        cmd(\"HSET\").arg(key).arg(field).arg(value)","highlight_start":1,"highlight_end":51},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets a single field in a hash if it does not exist.","highlight_start":1,"highlight_end":60},{"text":"    fn hset_nx<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(key: K, field: F, value: V) {","highlight_start":1,"highlight_end":93},{"text":"        cmd(\"HSETNX\").arg(key).arg(field).arg(value)","highlight_start":1,"highlight_end":53},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets a multiple fields in a hash.","highlight_start":1,"highlight_end":42},{"text":"    fn hset_multiple<K: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(key: K, items: &'a [(F, V)]) {","highlight_start":1,"highlight_end":100},{"text":"        cmd(\"HMSET\").arg(key).arg(items)","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Increments a value.","highlight_start":1,"highlight_end":28},{"text":"    fn hincr<K: ToRedisArgs, F: ToRedisArgs, D: ToRedisArgs>(key: K, field: F, delta: D) {","highlight_start":1,"highlight_end":91},{"text":"        cmd(if delta.describe_numeric_behavior() == NumericBehavior::NumberIsFloat {","highlight_start":1,"highlight_end":85},{"text":"            \"HINCRBYFLOAT\"","highlight_start":1,"highlight_end":27},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            \"HINCRBY\"","highlight_start":1,"highlight_end":22},{"text":"        }).arg(key).arg(field).arg(delta)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Checks if a field in a hash exists.","highlight_start":1,"highlight_end":44},{"text":"    fn hexists<K: ToRedisArgs, F: ToRedisArgs>(key: K, field: F) {","highlight_start":1,"highlight_end":67},{"text":"        cmd(\"HEXISTS\").arg(key).arg(field)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all the keys in a hash.","highlight_start":1,"highlight_end":37},{"text":"    fn hkeys<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":39},{"text":"        cmd(\"HKEYS\").arg(key)","highlight_start":1,"highlight_end":30},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all the values in a hash.","highlight_start":1,"highlight_end":39},{"text":"    fn hvals<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":39},{"text":"        cmd(\"HVALS\").arg(key)","highlight_start":1,"highlight_end":30},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets all the fields and values in a hash.","highlight_start":1,"highlight_end":50},{"text":"    fn hgetall<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":41},{"text":"        cmd(\"HGETALL\").arg(key)","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Gets the length of a hash.","highlight_start":1,"highlight_end":35},{"text":"    fn hlen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":38},{"text":"        cmd(\"HLEN\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // list operations","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop an element from a list, push it to another list","highlight_start":1,"highlight_end":60},{"text":"    /// and return it; or block until one is available","highlight_start":1,"highlight_end":55},{"text":"    fn blmove<S: ToRedisArgs, D: ToRedisArgs>(srckey: S, dstkey: D, src_dir: Direction, dst_dir: Direction, timeout: f64) {","highlight_start":1,"highlight_end":124},{"text":"        cmd(\"BLMOVE\").arg(srckey).arg(dstkey).arg(src_dir).arg(dst_dir).arg(timeout)","highlight_start":1,"highlight_end":85},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pops `count` elements from the first non-empty list key from the list of","highlight_start":1,"highlight_end":81},{"text":"    /// provided key names; or blocks until one is available.","highlight_start":1,"highlight_end":62},{"text":"    fn blmpop<K: ToRedisArgs>(timeout: f64, numkeys: usize, key: K, dir: Direction, count: usize){","highlight_start":1,"highlight_end":99},{"text":"        cmd(\"BLMPOP\").arg(timeout).arg(numkeys).arg(key).arg(dir).arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":90},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove and get the first element in a list, or block until one is available.","highlight_start":1,"highlight_end":85},{"text":"    fn blpop<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":53},{"text":"        cmd(\"BLPOP\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove and get the last element in a list, or block until one is available.","highlight_start":1,"highlight_end":84},{"text":"    fn brpop<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":53},{"text":"        cmd(\"BRPOP\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop a value from a list, push it to another list and return it;","highlight_start":1,"highlight_end":72},{"text":"    /// or block until one is available.","highlight_start":1,"highlight_end":41},{"text":"    fn brpoplpush<S: ToRedisArgs, D: ToRedisArgs>(srckey: S, dstkey: D, timeout: f64) {","highlight_start":1,"highlight_end":88},{"text":"        cmd(\"BRPOPLPUSH\").arg(srckey).arg(dstkey).arg(timeout)","highlight_start":1,"highlight_end":63},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get an element from a list by its index.","highlight_start":1,"highlight_end":49},{"text":"    fn lindex<K: ToRedisArgs>(key: K, index: isize) {","highlight_start":1,"highlight_end":54},{"text":"        cmd(\"LINDEX\").arg(key).arg(index)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert an element before another element in a list.","highlight_start":1,"highlight_end":60},{"text":"    fn linsert_before<K: ToRedisArgs, P: ToRedisArgs, V: ToRedisArgs>(","highlight_start":1,"highlight_end":71},{"text":"            key: K, pivot: P, value: V) {","highlight_start":1,"highlight_end":42},{"text":"        cmd(\"LINSERT\").arg(key).arg(\"BEFORE\").arg(pivot).arg(value)","highlight_start":1,"highlight_end":68},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert an element after another element in a list.","highlight_start":1,"highlight_end":59},{"text":"    fn linsert_after<K: ToRedisArgs, P: ToRedisArgs, V: ToRedisArgs>(","highlight_start":1,"highlight_end":70},{"text":"            key: K, pivot: P, value: V) {","highlight_start":1,"highlight_end":42},{"text":"        cmd(\"LINSERT\").arg(key).arg(\"AFTER\").arg(pivot).arg(value)","highlight_start":1,"highlight_end":67},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the length of the list stored at key.","highlight_start":1,"highlight_end":54},{"text":"    fn llen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":38},{"text":"        cmd(\"LLEN\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop an element a list, push it to another list and return it","highlight_start":1,"highlight_end":69},{"text":"    fn lmove<S: ToRedisArgs, D: ToRedisArgs>(srckey: S, dstkey: D, src_dir: Direction, dst_dir: Direction) {","highlight_start":1,"highlight_end":109},{"text":"        cmd(\"LMOVE\").arg(srckey).arg(dstkey).arg(src_dir).arg(dst_dir)","highlight_start":1,"highlight_end":71},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pops `count` elements from the first non-empty list key from the list of","highlight_start":1,"highlight_end":81},{"text":"    /// provided key names.","highlight_start":1,"highlight_end":28},{"text":"    fn lmpop<K: ToRedisArgs>( numkeys: usize, key: K, dir: Direction, count: usize) {","highlight_start":1,"highlight_end":86},{"text":"        cmd(\"LMPOP\").arg(numkeys).arg(key).arg(dir).arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":76},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the up to `count` first elements of the list stored at key.","highlight_start":1,"highlight_end":88},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// If `count` is not specified, then defaults to first element.","highlight_start":1,"highlight_end":69},{"text":"    fn lpop<K: ToRedisArgs>(key: K, count: Option<core::num::NonZeroUsize>) {","highlight_start":1,"highlight_end":78},{"text":"        cmd(\"LPOP\").arg(key).arg(count)","highlight_start":1,"highlight_end":40},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the index of the first matching value of the list stored at key.","highlight_start":1,"highlight_end":81},{"text":"    fn lpos<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V, options: LposOptions) {","highlight_start":1,"highlight_end":86},{"text":"        cmd(\"LPOS\").arg(key).arg(value).arg(options)","highlight_start":1,"highlight_end":53},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert all the specified values at the head of the list stored at key.","highlight_start":1,"highlight_end":79},{"text":"    fn lpush<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":65},{"text":"        cmd(\"LPUSH\").arg(key).arg(value)","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Inserts a value at the head of the list stored at key, only if key","highlight_start":1,"highlight_end":75},{"text":"    /// already exists and holds a list.","highlight_start":1,"highlight_end":41},{"text":"    fn lpush_exists<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":72},{"text":"        cmd(\"LPUSHX\").arg(key).arg(value)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the specified elements of the list stored at key.","highlight_start":1,"highlight_end":66},{"text":"    fn lrange<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":67},{"text":"        cmd(\"LRANGE\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":52},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes the first count occurrences of elements equal to value","highlight_start":1,"highlight_end":71},{"text":"    /// from the list stored at key.","highlight_start":1,"highlight_end":37},{"text":"    fn lrem<K: ToRedisArgs, V: ToRedisArgs>(key: K, count: isize, value: V) {","highlight_start":1,"highlight_end":78},{"text":"        cmd(\"LREM\").arg(key).arg(count).arg(value)","highlight_start":1,"highlight_end":51},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Trim an existing list so that it will contain only the specified","highlight_start":1,"highlight_end":73},{"text":"    /// range of elements specified.","highlight_start":1,"highlight_end":37},{"text":"    fn ltrim<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":66},{"text":"        cmd(\"LTRIM\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":51},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Sets the list element at index to value","highlight_start":1,"highlight_end":48},{"text":"    fn lset<K: ToRedisArgs, V: ToRedisArgs>(key: K, index: isize, value: V) {","highlight_start":1,"highlight_end":78},{"text":"        cmd(\"LSET\").arg(key).arg(index).arg(value)","highlight_start":1,"highlight_end":51},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the up to `count` last elements of the list stored at key","highlight_start":1,"highlight_end":86},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// If `count` is not specified, then defaults to last element.","highlight_start":1,"highlight_end":68},{"text":"    fn rpop<K: ToRedisArgs>(key: K, count: Option<core::num::NonZeroUsize>) {","highlight_start":1,"highlight_end":78},{"text":"        cmd(\"RPOP\").arg(key).arg(count)","highlight_start":1,"highlight_end":40},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Pop a value from a list, push it to another list and return it.","highlight_start":1,"highlight_end":72},{"text":"    fn rpoplpush<K: ToRedisArgs, D: ToRedisArgs>(key: K, dstkey: D) {","highlight_start":1,"highlight_end":70},{"text":"        cmd(\"RPOPLPUSH\").arg(key).arg(dstkey)","highlight_start":1,"highlight_end":46},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Insert all the specified values at the tail of the list stored at key.","highlight_start":1,"highlight_end":79},{"text":"    fn rpush<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":65},{"text":"        cmd(\"RPUSH\").arg(key).arg(value)","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Inserts value at the tail of the list stored at key, only if key","highlight_start":1,"highlight_end":73},{"text":"    /// already exists and holds a list.","highlight_start":1,"highlight_end":41},{"text":"    fn rpush_exists<K: ToRedisArgs, V: ToRedisArgs>(key: K, value: V) {","highlight_start":1,"highlight_end":72},{"text":"        cmd(\"RPUSHX\").arg(key).arg(value)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // set commands","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add one or more members to a set.","highlight_start":1,"highlight_end":42},{"text":"    fn sadd<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":65},{"text":"        cmd(\"SADD\").arg(key).arg(member)","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the number of members in a set.","highlight_start":1,"highlight_end":44},{"text":"    fn scard<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":39},{"text":"        cmd(\"SCARD\").arg(key)","highlight_start":1,"highlight_end":30},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Subtract multiple sets.","highlight_start":1,"highlight_end":32},{"text":"    fn sdiff<K: ToRedisArgs>(keys: K) {","highlight_start":1,"highlight_end":40},{"text":"        cmd(\"SDIFF\").arg(keys)","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Subtract multiple sets and store the resulting set in a key.","highlight_start":1,"highlight_end":69},{"text":"    fn sdiffstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: K) {","highlight_start":1,"highlight_end":72},{"text":"        cmd(\"SDIFFSTORE\").arg(dstkey).arg(keys)","highlight_start":1,"highlight_end":48},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sets.","highlight_start":1,"highlight_end":33},{"text":"    fn sinter<K: ToRedisArgs>(keys: K) {","highlight_start":1,"highlight_end":41},{"text":"        cmd(\"SINTER\").arg(keys)","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sets and store the resulting set in a key.","highlight_start":1,"highlight_end":70},{"text":"    fn sinterstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: K) {","highlight_start":1,"highlight_end":73},{"text":"        cmd(\"SINTERSTORE\").arg(dstkey).arg(keys)","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine if a given value is a member of a set.","highlight_start":1,"highlight_end":57},{"text":"    fn sismember<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":70},{"text":"        cmd(\"SISMEMBER\").arg(key).arg(member)","highlight_start":1,"highlight_end":46},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get all the members in a set.","highlight_start":1,"highlight_end":38},{"text":"    fn smembers<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":42},{"text":"        cmd(\"SMEMBERS\").arg(key)","highlight_start":1,"highlight_end":33},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Move a member from one set to another.","highlight_start":1,"highlight_end":47},{"text":"    fn smove<S: ToRedisArgs, D: ToRedisArgs, M: ToRedisArgs>(srckey: S, dstkey: D, member: M) {","highlight_start":1,"highlight_end":96},{"text":"        cmd(\"SMOVE\").arg(srckey).arg(dstkey).arg(member)","highlight_start":1,"highlight_end":57},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove and return a random member from a set.","highlight_start":1,"highlight_end":54},{"text":"    fn spop<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":38},{"text":"        cmd(\"SPOP\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get one random member from a set.","highlight_start":1,"highlight_end":42},{"text":"    fn srandmember<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":45},{"text":"        cmd(\"SRANDMEMBER\").arg(key)","highlight_start":1,"highlight_end":36},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get multiple random members from a set.","highlight_start":1,"highlight_end":48},{"text":"    fn srandmember_multiple<K: ToRedisArgs>(key: K, count: usize) {","highlight_start":1,"highlight_end":68},{"text":"        cmd(\"SRANDMEMBER\").arg(key).arg(count)","highlight_start":1,"highlight_end":47},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove one or more members from a set.","highlight_start":1,"highlight_end":47},{"text":"    fn srem<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":65},{"text":"        cmd(\"SREM\").arg(key).arg(member)","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add multiple sets.","highlight_start":1,"highlight_end":27},{"text":"    fn sunion<K: ToRedisArgs>(keys: K) {","highlight_start":1,"highlight_end":41},{"text":"        cmd(\"SUNION\").arg(keys)","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add multiple sets and store the resulting set in a key.","highlight_start":1,"highlight_end":64},{"text":"    fn sunionstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: K) {","highlight_start":1,"highlight_end":73},{"text":"        cmd(\"SUNIONSTORE\").arg(dstkey).arg(keys)","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // sorted set commands","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add one member to a sorted set, or update its score if it already exists.","highlight_start":1,"highlight_end":82},{"text":"    fn zadd<K: ToRedisArgs, S: ToRedisArgs, M: ToRedisArgs>(key: K, member: M, score: S) {","highlight_start":1,"highlight_end":91},{"text":"        cmd(\"ZADD\").arg(key).arg(score).arg(member)","highlight_start":1,"highlight_end":52},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add multiple members to a sorted set, or update its score if it already exists.","highlight_start":1,"highlight_end":88},{"text":"    fn zadd_multiple<K: ToRedisArgs, S: ToRedisArgs, M: ToRedisArgs>(key: K, items: &'a [(S, M)]) {","highlight_start":1,"highlight_end":100},{"text":"        cmd(\"ZADD\").arg(key).arg(items)","highlight_start":1,"highlight_end":40},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the number of members in a sorted set.","highlight_start":1,"highlight_end":51},{"text":"    fn zcard<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":39},{"text":"        cmd(\"ZCARD\").arg(key)","highlight_start":1,"highlight_end":30},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count the members in a sorted set with scores within the given values.","highlight_start":1,"highlight_end":79},{"text":"    fn zcount<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":90},{"text":"        cmd(\"ZCOUNT\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Increments the member in a sorted set at key by delta.","highlight_start":1,"highlight_end":63},{"text":"    /// If the member does not exist, it is added with delta as its score.","highlight_start":1,"highlight_end":75},{"text":"    fn zincr<K: ToRedisArgs, M: ToRedisArgs, D: ToRedisArgs>(key: K, member: M, delta: D) {","highlight_start":1,"highlight_end":92},{"text":"        cmd(\"ZINCRBY\").arg(key).arg(delta).arg(member)","highlight_start":1,"highlight_end":55},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":77},{"text":"    /// a new key using SUM as aggregation function.","highlight_start":1,"highlight_end":53},{"text":"    fn zinterstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":79},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys)","highlight_start":1,"highlight_end":65},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":77},{"text":"    /// a new key using MIN as aggregation function.","highlight_start":1,"highlight_end":53},{"text":"    fn zinterstore_min<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":83},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\")","highlight_start":1,"highlight_end":93},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Intersect multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":77},{"text":"    /// a new key using MAX as aggregation function.","highlight_start":1,"highlight_end":53},{"text":"    fn zinterstore_max<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":83},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\")","highlight_start":1,"highlight_end":93},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zinterstore`], but with the ability to specify a","highlight_start":1,"highlight_end":69},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":79},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":20},{"text":"    fn zinterstore_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":108},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":106},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":93},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zinterstore_min`], but with the ability to specify a","highlight_start":1,"highlight_end":73},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":79},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":20},{"text":"    fn zinterstore_min_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":112},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":106},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":121},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zinterstore_max`], but with the ability to specify a","highlight_start":1,"highlight_end":73},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":79},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":20},{"text":"    fn zinterstore_max_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":112},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":106},{"text":"        cmd(\"ZINTERSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":121},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Count the number of members in a sorted set between a given lexicographical range.","highlight_start":1,"highlight_end":91},{"text":"    fn zlexcount<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":93},{"text":"        cmd(\"ZLEXCOUNT\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":52},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the member with the highest score in a sorted set.","highlight_start":1,"highlight_end":79},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":54},{"text":"    fn bzpopmax<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":56},{"text":"        cmd(\"BZPOPMAX\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":46},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the highest scores in a sorted set","highlight_start":1,"highlight_end":88},{"text":"    fn zpopmax<K: ToRedisArgs>(key: K, count: isize) {","highlight_start":1,"highlight_end":55},{"text":"        cmd(\"ZPOPMAX\").arg(key).arg(count)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns the member with the lowest score in a sorted set.","highlight_start":1,"highlight_end":78},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":54},{"text":"    fn bzpopmin<K: ToRedisArgs>(key: K, timeout: f64) {","highlight_start":1,"highlight_end":56},{"text":"        cmd(\"BZPOPMIN\").arg(key).arg(timeout)","highlight_start":1,"highlight_end":46},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the lowest scores in a sorted set","highlight_start":1,"highlight_end":87},{"text":"    fn zpopmin<K: ToRedisArgs>(key: K, count: isize) {","highlight_start":1,"highlight_end":55},{"text":"        cmd(\"ZPOPMIN\").arg(key).arg(count)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the highest scores,","highlight_start":1,"highlight_end":73},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":79},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":54},{"text":"    fn bzmpop_max<K: ToRedisArgs>(timeout: f64, keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":79},{"text":"        cmd(\"BZMPOP\").arg(timeout).arg(keys.len()).arg(keys).arg(\"MAX\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":96},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the highest scores,","highlight_start":1,"highlight_end":73},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":79},{"text":"    fn zmpop_max<K: ToRedisArgs>(keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":64},{"text":"        cmd(\"ZMPOP\").arg(keys.len()).arg(keys).arg(\"MAX\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":82},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the lowest scores,","highlight_start":1,"highlight_end":72},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":79},{"text":"    /// Blocks until a member is available otherwise.","highlight_start":1,"highlight_end":54},{"text":"    fn bzmpop_min<K: ToRedisArgs>(timeout: f64, keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":79},{"text":"        cmd(\"BZMPOP\").arg(timeout).arg(keys.len()).arg(keys).arg(\"MIN\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":96},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Removes and returns up to count members with the lowest scores,","highlight_start":1,"highlight_end":72},{"text":"    /// from the first non-empty sorted set in the provided list of key names.","highlight_start":1,"highlight_end":79},{"text":"    fn zmpop_min<K: ToRedisArgs>(keys: &'a [K], count: isize) {","highlight_start":1,"highlight_end":64},{"text":"        cmd(\"ZMPOP\").arg(keys.len()).arg(keys).arg(\"MIN\").arg(\"COUNT\").arg(count)","highlight_start":1,"highlight_end":82},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return up to count random members in a sorted set (or 1 if `count == None`)","highlight_start":1,"highlight_end":84},{"text":"    fn zrandmember<K: ToRedisArgs>(key: K, count: Option<isize>) {","highlight_start":1,"highlight_end":67},{"text":"        cmd(\"ZRANDMEMBER\").arg(key).arg(count)","highlight_start":1,"highlight_end":47},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return up to count random members in a sorted set with scores","highlight_start":1,"highlight_end":70},{"text":"    fn zrandmember_withscores<K: ToRedisArgs>(key: K, count: isize) {","highlight_start":1,"highlight_end":70},{"text":"        cmd(\"ZRANDMEMBER\").arg(key).arg(count).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":65},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index","highlight_start":1,"highlight_end":60},{"text":"    fn zrange<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":67},{"text":"        cmd(\"ZRANGE\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":52},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index with scores.","highlight_start":1,"highlight_end":73},{"text":"    fn zrange_withscores<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":78},{"text":"        cmd(\"ZRANGE\").arg(key).arg(start).arg(stop).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":70},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical range.","highlight_start":1,"highlight_end":77},{"text":"    fn zrangebylex<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":95},{"text":"        cmd(\"ZRANGEBYLEX\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":54},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical","highlight_start":1,"highlight_end":70},{"text":"    /// range with offset and limit.","highlight_start":1,"highlight_end":37},{"text":"    fn zrangebylex_limit<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(","highlight_start":1,"highlight_end":75},{"text":"            key: K, min: M, max: MM, offset: isize, count: isize) {","highlight_start":1,"highlight_end":68},{"text":"        cmd(\"ZRANGEBYLEX\").arg(key).arg(min).arg(max).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":90},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical range.","highlight_start":1,"highlight_end":77},{"text":"    fn zrevrangebylex<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(key: K, max: MM, min: M) {","highlight_start":1,"highlight_end":98},{"text":"        cmd(\"ZREVRANGEBYLEX\").arg(key).arg(max).arg(min)","highlight_start":1,"highlight_end":57},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by lexicographical","highlight_start":1,"highlight_end":70},{"text":"    /// range with offset and limit.","highlight_start":1,"highlight_end":37},{"text":"    fn zrevrangebylex_limit<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(","highlight_start":1,"highlight_end":78},{"text":"            key: K, max: MM, min: M, offset: isize, count: isize) {","highlight_start":1,"highlight_end":68},{"text":"        cmd(\"ZREVRANGEBYLEX\").arg(key).arg(max).arg(min).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":93},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score.","highlight_start":1,"highlight_end":61},{"text":"    fn zrangebyscore<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":97},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":56},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with scores.","highlight_start":1,"highlight_end":73},{"text":"    fn zrangebyscore_withscores<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":108},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":74},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit.","highlight_start":1,"highlight_end":72},{"text":"    fn zrangebyscore_limit<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>","highlight_start":1,"highlight_end":76},{"text":"            (key: K, min: M, max: MM, offset: isize, count: isize) {","highlight_start":1,"highlight_end":69},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":92},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit with scores.","highlight_start":1,"highlight_end":84},{"text":"    fn zrangebyscore_limit_withscores<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>","highlight_start":1,"highlight_end":87},{"text":"            (key: K, min: M, max: MM, offset: isize, count: isize) {","highlight_start":1,"highlight_end":69},{"text":"        cmd(\"ZRANGEBYSCORE\").arg(key).arg(min).arg(max).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":74},{"text":"            .arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine the index of a member in a sorted set.","highlight_start":1,"highlight_end":57},{"text":"    fn zrank<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":66},{"text":"        cmd(\"ZRANK\").arg(key).arg(member)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove one or more members from a sorted set.","highlight_start":1,"highlight_end":54},{"text":"    fn zrem<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":66},{"text":"        cmd(\"ZREM\").arg(key).arg(members)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove all members in a sorted set between the given lexicographical range.","highlight_start":1,"highlight_end":84},{"text":"    fn zrembylex<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":93},{"text":"        cmd(\"ZREMRANGEBYLEX\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":57},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove all members in a sorted set within the given indexes.","highlight_start":1,"highlight_end":69},{"text":"    fn zremrangebyrank<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":76},{"text":"        cmd(\"ZREMRANGEBYRANK\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":61},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Remove all members in a sorted set within the given scores.","highlight_start":1,"highlight_end":68},{"text":"    fn zrembyscore<K: ToRedisArgs, M: ToRedisArgs, MM: ToRedisArgs>(key: K, min: M, max: MM) {","highlight_start":1,"highlight_end":95},{"text":"        cmd(\"ZREMRANGEBYSCORE\").arg(key).arg(min).arg(max)","highlight_start":1,"highlight_end":59},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index, with scores","highlight_start":1,"highlight_end":73},{"text":"    /// ordered from high to low.","highlight_start":1,"highlight_end":34},{"text":"    fn zrevrange<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":70},{"text":"        cmd(\"ZREVRANGE\").arg(key).arg(start).arg(stop)","highlight_start":1,"highlight_end":55},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by index, with scores","highlight_start":1,"highlight_end":73},{"text":"    /// ordered from high to low.","highlight_start":1,"highlight_end":34},{"text":"    fn zrevrange_withscores<K: ToRedisArgs>(key: K, start: isize, stop: isize) {","highlight_start":1,"highlight_end":81},{"text":"        cmd(\"ZREVRANGE\").arg(key).arg(start).arg(stop).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":73},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score.","highlight_start":1,"highlight_end":61},{"text":"    fn zrevrangebyscore<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(key: K, max: MM, min: M) {","highlight_start":1,"highlight_end":100},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min)","highlight_start":1,"highlight_end":59},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with scores.","highlight_start":1,"highlight_end":73},{"text":"    fn zrevrangebyscore_withscores<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>(key: K, max: MM, min: M) {","highlight_start":1,"highlight_end":111},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":77},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit.","highlight_start":1,"highlight_end":72},{"text":"    fn zrevrangebyscore_limit<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>","highlight_start":1,"highlight_end":79},{"text":"            (key: K, max: MM, min: M, offset: isize, count: isize) {","highlight_start":1,"highlight_end":69},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min).arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":95},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return a range of members in a sorted set, by score with limit with scores.","highlight_start":1,"highlight_end":84},{"text":"    fn zrevrangebyscore_limit_withscores<K: ToRedisArgs, MM: ToRedisArgs, M: ToRedisArgs>","highlight_start":1,"highlight_end":90},{"text":"            (key: K, max: MM, min: M, offset: isize, count: isize) {","highlight_start":1,"highlight_end":69},{"text":"        cmd(\"ZREVRANGEBYSCORE\").arg(key).arg(max).arg(min).arg(\"WITHSCORES\")","highlight_start":1,"highlight_end":77},{"text":"            .arg(\"LIMIT\").arg(offset).arg(count)","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Determine the index of a member in a sorted set, with scores ordered from high to low.","highlight_start":1,"highlight_end":95},{"text":"    fn zrevrank<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":69},{"text":"        cmd(\"ZREVRANK\").arg(key).arg(member)","highlight_start":1,"highlight_end":45},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the score associated with the given member in a sorted set.","highlight_start":1,"highlight_end":72},{"text":"    fn zscore<K: ToRedisArgs, M: ToRedisArgs>(key: K, member: M) {","highlight_start":1,"highlight_end":67},{"text":"        cmd(\"ZSCORE\").arg(key).arg(member)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Get the scores associated with multiple members in a sorted set.","highlight_start":1,"highlight_end":73},{"text":"    fn zscore_multiple<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: &'a [M]) {","highlight_start":1,"highlight_end":83},{"text":"        cmd(\"ZMSCORE\").arg(key).arg(members)","highlight_start":1,"highlight_end":45},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unions multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":74},{"text":"    /// a new key using SUM as aggregation function.","highlight_start":1,"highlight_end":53},{"text":"    fn zunionstore<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":79},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys)","highlight_start":1,"highlight_end":65},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unions multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":74},{"text":"    /// a new key using MIN as aggregation function.","highlight_start":1,"highlight_end":53},{"text":"    fn zunionstore_min<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":83},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\")","highlight_start":1,"highlight_end":93},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Unions multiple sorted sets and store the resulting sorted set in","highlight_start":1,"highlight_end":74},{"text":"    /// a new key using MAX as aggregation function.","highlight_start":1,"highlight_end":53},{"text":"    fn zunionstore_max<D: ToRedisArgs, K: ToRedisArgs>(dstkey: D, keys: &'a [K]) {","highlight_start":1,"highlight_end":83},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\")","highlight_start":1,"highlight_end":93},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zunionstore`], but with the ability to specify a","highlight_start":1,"highlight_end":69},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":79},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":20},{"text":"    fn zunionstore_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":108},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":106},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":93},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zunionstore_min`], but with the ability to specify a","highlight_start":1,"highlight_end":73},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":79},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":20},{"text":"    fn zunionstore_min_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":112},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":106},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MIN\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":121},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// [`Commands::zunionstore_max`], but with the ability to specify a","highlight_start":1,"highlight_end":73},{"text":"    /// multiplication factor for each sorted set by pairing one with each key","highlight_start":1,"highlight_end":79},{"text":"    /// in a tuple.","highlight_start":1,"highlight_end":20},{"text":"    fn zunionstore_max_weights<D: ToRedisArgs, K: ToRedisArgs, W: ToRedisArgs>(dstkey: D, keys: &'a [(K, W)]) {","highlight_start":1,"highlight_end":112},{"text":"        let (keys, weights): (Vec<&K>, Vec<&W>) = keys.iter().map(|(key, weight)| (key, weight)).unzip();","highlight_start":1,"highlight_end":106},{"text":"        cmd(\"ZUNIONSTORE\").arg(dstkey).arg(keys.len()).arg(keys).arg(\"AGGREGATE\").arg(\"MAX\").arg(\"WEIGHTS\").arg(weights)","highlight_start":1,"highlight_end":121},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // hyperloglog commands","highlight_start":1,"highlight_end":28},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Adds the specified elements to the specified HyperLogLog.","highlight_start":1,"highlight_end":66},{"text":"    fn pfadd<K: ToRedisArgs, E: ToRedisArgs>(key: K, element: E) {","highlight_start":1,"highlight_end":67},{"text":"        cmd(\"PFADD\").arg(key).arg(element)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the approximated cardinality of the set(s) observed by the","highlight_start":1,"highlight_end":74},{"text":"    /// HyperLogLog at key(s).","highlight_start":1,"highlight_end":31},{"text":"    fn pfcount<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":41},{"text":"        cmd(\"PFCOUNT\").arg(key)","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Merge N different HyperLogLogs into a single one.","highlight_start":1,"highlight_end":58},{"text":"    fn pfmerge<D: ToRedisArgs, S: ToRedisArgs>(dstkey: D, srckeys: S) {","highlight_start":1,"highlight_end":72},{"text":"        cmd(\"PFMERGE\").arg(dstkey).arg(srckeys)","highlight_start":1,"highlight_end":48},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Posts a message to the given channel.","highlight_start":1,"highlight_end":46},{"text":"    fn publish<K: ToRedisArgs, E: ToRedisArgs>(channel: K, message: E) {","highlight_start":1,"highlight_end":73},{"text":"        cmd(\"PUBLISH\").arg(channel).arg(message)","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Object commands","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the encoding of a key.","highlight_start":1,"highlight_end":39},{"text":"    fn object_encoding<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":49},{"text":"        cmd(\"OBJECT\").arg(\"ENCODING\").arg(key)","highlight_start":1,"highlight_end":47},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the time in seconds since the last access of a key.","highlight_start":1,"highlight_end":68},{"text":"    fn object_idletime<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":49},{"text":"        cmd(\"OBJECT\").arg(\"IDLETIME\").arg(key)","highlight_start":1,"highlight_end":47},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the logarithmic access frequency counter of a key.","highlight_start":1,"highlight_end":67},{"text":"    fn object_freq<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":45},{"text":"        cmd(\"OBJECT\").arg(\"FREQ\").arg(key)","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the reference count of a key.","highlight_start":1,"highlight_end":46},{"text":"    fn object_refcount<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":49},{"text":"        cmd(\"OBJECT\").arg(\"REFCOUNT\").arg(key)","highlight_start":1,"highlight_end":47},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // ACL commands","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// When Redis is configured to use an ACL file (with the aclfile","highlight_start":1,"highlight_end":70},{"text":"    /// configuration option), this command will reload the ACLs from the file,","highlight_start":1,"highlight_end":80},{"text":"    /// replacing all the current ACL rules with the ones defined in the file.","highlight_start":1,"highlight_end":79},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_load<>() {","highlight_start":1,"highlight_end":22},{"text":"        cmd(\"ACL\").arg(\"LOAD\")","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// When Redis is configured to use an ACL file (with the aclfile","highlight_start":1,"highlight_end":70},{"text":"    /// configuration option), this command will save the currently defined","highlight_start":1,"highlight_end":76},{"text":"    /// ACLs from the server memory to the ACL file.","highlight_start":1,"highlight_end":53},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_save<>() {","highlight_start":1,"highlight_end":22},{"text":"        cmd(\"ACL\").arg(\"SAVE\")","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows the currently active ACL rules in the Redis server.","highlight_start":1,"highlight_end":66},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_list<>() {","highlight_start":1,"highlight_end":22},{"text":"        cmd(\"ACL\").arg(\"LIST\")","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows a list of all the usernames of the currently configured users in","highlight_start":1,"highlight_end":79},{"text":"    /// the Redis ACL system.","highlight_start":1,"highlight_end":30},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_users<>() {","highlight_start":1,"highlight_end":23},{"text":"        cmd(\"ACL\").arg(\"USERS\")","highlight_start":1,"highlight_end":32},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns all the rules defined for an existing ACL user.","highlight_start":1,"highlight_end":64},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_getuser<K: ToRedisArgs>(username: K) {","highlight_start":1,"highlight_end":50},{"text":"        cmd(\"ACL\").arg(\"GETUSER\").arg(username)","highlight_start":1,"highlight_end":48},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Creates an ACL user without any privilege.","highlight_start":1,"highlight_end":51},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_setuser<K: ToRedisArgs>(username: K) {","highlight_start":1,"highlight_end":50},{"text":"        cmd(\"ACL\").arg(\"SETUSER\").arg(username)","highlight_start":1,"highlight_end":48},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Creates an ACL user with the specified rules or modify the rules of","highlight_start":1,"highlight_end":76},{"text":"    /// an existing user.","highlight_start":1,"highlight_end":26},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_setuser_rules<K: ToRedisArgs>(username: K, rules: &'a [acl::Rule]) {","highlight_start":1,"highlight_end":80},{"text":"        cmd(\"ACL\").arg(\"SETUSER\").arg(username).arg(rules)","highlight_start":1,"highlight_end":59},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Delete all the specified ACL users and terminate all the connections","highlight_start":1,"highlight_end":77},{"text":"    /// that are authenticated with such users.","highlight_start":1,"highlight_end":48},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_deluser<K: ToRedisArgs>(usernames: &'a [K]) {","highlight_start":1,"highlight_end":57},{"text":"        cmd(\"ACL\").arg(\"DELUSER\").arg(usernames)","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows the available ACL categories.","highlight_start":1,"highlight_end":44},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_cat<>() {","highlight_start":1,"highlight_end":21},{"text":"        cmd(\"ACL\").arg(\"CAT\")","highlight_start":1,"highlight_end":30},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows all the Redis commands in the specified category.","highlight_start":1,"highlight_end":64},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_cat_categoryname<K: ToRedisArgs>(categoryname: K) {","highlight_start":1,"highlight_end":63},{"text":"        cmd(\"ACL\").arg(\"CAT\").arg(categoryname)","highlight_start":1,"highlight_end":48},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Generates a 256-bits password starting from /dev/urandom if available.","highlight_start":1,"highlight_end":79},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_genpass<>() {","highlight_start":1,"highlight_end":25},{"text":"        cmd(\"ACL\").arg(\"GENPASS\")","highlight_start":1,"highlight_end":34},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Generates a 1-to-1024-bits password starting from /dev/urandom if available.","highlight_start":1,"highlight_end":85},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_genpass_bits<>(bits: isize) {","highlight_start":1,"highlight_end":41},{"text":"        cmd(\"ACL\").arg(\"GENPASS\").arg(bits)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the username the current connection is authenticated with.","highlight_start":1,"highlight_end":75},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_whoami<>() {","highlight_start":1,"highlight_end":24},{"text":"        cmd(\"ACL\").arg(\"WHOAMI\")","highlight_start":1,"highlight_end":33},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Shows a list of recent ACL security events","highlight_start":1,"highlight_end":51},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_log<>(count: isize) {","highlight_start":1,"highlight_end":33},{"text":"        cmd(\"ACL\").arg(\"LOG\").arg(count)","highlight_start":1,"highlight_end":41},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Clears the ACL log.","highlight_start":1,"highlight_end":28},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_log_reset<>() {","highlight_start":1,"highlight_end":27},{"text":"        cmd(\"ACL\").arg(\"LOG\").arg(\"RESET\")","highlight_start":1,"highlight_end":43},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns a helpful text describing the different subcommands.","highlight_start":1,"highlight_end":69},{"text":"    #[cfg(feature = \"acl\")]","highlight_start":1,"highlight_end":28},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"acl\")))]","highlight_start":1,"highlight_end":51},{"text":"    fn acl_help<>() {","highlight_start":1,"highlight_end":22},{"text":"        cmd(\"ACL\").arg(\"HELP\")","highlight_start":1,"highlight_end":31},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    //","highlight_start":1,"highlight_end":7},{"text":"    // geospatial commands","highlight_start":1,"highlight_end":27},{"text":"    //","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Adds the specified geospatial items to the specified key.","highlight_start":1,"highlight_end":66},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Every member has to be written as a tuple of `(longitude, latitude,","highlight_start":1,"highlight_end":76},{"text":"    /// member_name)`. It can be a single tuple, or a vector of tuples.","highlight_start":1,"highlight_end":72},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// `longitude, latitude` can be set using [`redis::geo::Coord`][1].","highlight_start":1,"highlight_end":73},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// [1]: ./geo/struct.Coord.html","highlight_start":1,"highlight_end":37},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Returns the number of elements added to the sorted set, not including","highlight_start":1,"highlight_end":78},{"text":"    /// elements already existing for which the score was updated.","highlight_start":1,"highlight_end":67},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// # Example","highlight_start":1,"highlight_end":18},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":23},{"text":"    /// use redis::{Commands, Connection, RedisResult};","highlight_start":1,"highlight_end":56},{"text":"    /// use redis::geo::Coord;","highlight_start":1,"highlight_end":31},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// fn add_point(con: &mut Connection) -> RedisResult<isize> {","highlight_start":1,"highlight_end":67},{"text":"    ///     con.geo_add(\"my_gis\", (Coord::lon_lat(13.361389, 38.115556), \"Palermo\"))","highlight_start":1,"highlight_end":85},{"text":"    /// }","highlight_start":1,"highlight_end":10},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// fn add_point_with_tuples(con: &mut Connection) -> RedisResult<isize> {","highlight_start":1,"highlight_end":79},{"text":"    ///     con.geo_add(\"my_gis\", (\"13.361389\", \"38.115556\", \"Palermo\"))","highlight_start":1,"highlight_end":73},{"text":"    /// }","highlight_start":1,"highlight_end":10},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// fn add_many_points(con: &mut Connection) -> RedisResult<isize> {","highlight_start":1,"highlight_end":73},{"text":"    ///     con.geo_add(\"my_gis\", &[","highlight_start":1,"highlight_end":37},{"text":"    ///         (\"13.361389\", \"38.115556\", \"Palermo\"),","highlight_start":1,"highlight_end":55},{"text":"    ///         (\"15.087269\", \"37.502669\", \"Catania\")","highlight_start":1,"highlight_end":54},{"text":"    ///     ])","highlight_start":1,"highlight_end":15},{"text":"    /// }","highlight_start":1,"highlight_end":10},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":35},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":58},{"text":"    fn geo_add<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":69},{"text":"        cmd(\"GEOADD\").arg(key).arg(members)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the distance between two members in the geospatial index","highlight_start":1,"highlight_end":72},{"text":"    /// represented by the sorted set.","highlight_start":1,"highlight_end":39},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// If one or both the members are missing, the command returns NULL, so","highlight_start":1,"highlight_end":77},{"text":"    /// it may be convenient to parse its response as either `Option<f64>` or","highlight_start":1,"highlight_end":78},{"text":"    /// `Option<String>`.","highlight_start":1,"highlight_end":26},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// # Example","highlight_start":1,"highlight_end":18},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":23},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":44},{"text":"    /// use redis::geo::Unit;","highlight_start":1,"highlight_end":30},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// fn get_dists(con: &mut redis::Connection) {","highlight_start":1,"highlight_end":52},{"text":"    ///     let x: RedisResult<f64> = con.geo_dist(","highlight_start":1,"highlight_end":52},{"text":"    ///         \"my_gis\",","highlight_start":1,"highlight_end":26},{"text":"    ///         \"Palermo\",","highlight_start":1,"highlight_end":27},{"text":"    ///         \"Catania\",","highlight_start":1,"highlight_end":27},{"text":"    ///         Unit::Kilometers","highlight_start":1,"highlight_end":33},{"text":"    ///     );","highlight_start":1,"highlight_end":15},{"text":"    ///     // x is Ok(166.2742)","highlight_start":1,"highlight_end":33},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    ///     let x: RedisResult<Option<f64>> = con.geo_dist(","highlight_start":1,"highlight_end":60},{"text":"    ///         \"my_gis\",","highlight_start":1,"highlight_end":26},{"text":"    ///         \"Palermo\",","highlight_start":1,"highlight_end":27},{"text":"    ///         \"Atlantis\",","highlight_start":1,"highlight_end":28},{"text":"    ///         Unit::Meters","highlight_start":1,"highlight_end":29},{"text":"    ///     );","highlight_start":1,"highlight_end":15},{"text":"    ///     // x is Ok(None)","highlight_start":1,"highlight_end":29},{"text":"    /// }","highlight_start":1,"highlight_end":10},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":35},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":58},{"text":"    fn geo_dist<K: ToRedisArgs, M1: ToRedisArgs, M2: ToRedisArgs>(","highlight_start":1,"highlight_end":67},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        member1: M1,","highlight_start":1,"highlight_end":21},{"text":"        member2: M2,","highlight_start":1,"highlight_end":21},{"text":"        unit: geo::Unit","highlight_start":1,"highlight_end":24},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"GEODIST\")","highlight_start":1,"highlight_end":23},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(member1)","highlight_start":1,"highlight_end":26},{"text":"            .arg(member2)","highlight_start":1,"highlight_end":26},{"text":"            .arg(unit)","highlight_start":1,"highlight_end":23},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return valid [Geohash][1] strings representing the position of one or","highlight_start":1,"highlight_end":78},{"text":"    /// more members of the geospatial index represented by the sorted set at","highlight_start":1,"highlight_end":78},{"text":"    /// key.","highlight_start":1,"highlight_end":13},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// [1]: https://en.wikipedia.org/wiki/Geohash","highlight_start":1,"highlight_end":51},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// # Example","highlight_start":1,"highlight_end":18},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":23},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":44},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// fn get_hash(con: &mut redis::Connection) {","highlight_start":1,"highlight_end":51},{"text":"    ///     let x: RedisResult<Vec<String>> = con.geo_hash(\"my_gis\", \"Palermo\");","highlight_start":1,"highlight_end":81},{"text":"    ///     // x is vec![\"sqc8b49rny0\"]","highlight_start":1,"highlight_end":40},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    ///     let x: RedisResult<Vec<String>> = con.geo_hash(\"my_gis\", &[\"Palermo\", \"Catania\"]);","highlight_start":1,"highlight_end":95},{"text":"    ///     // x is vec![\"sqc8b49rny0\", \"sqdtr74hyu0\"]","highlight_start":1,"highlight_end":55},{"text":"    /// }","highlight_start":1,"highlight_end":10},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":35},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":58},{"text":"    fn geo_hash<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":70},{"text":"        cmd(\"GEOHASH\").arg(key).arg(members)","highlight_start":1,"highlight_end":45},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the positions of all the specified members of the geospatial","highlight_start":1,"highlight_end":76},{"text":"    /// index represented by the sorted set at key.","highlight_start":1,"highlight_end":52},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Every position is a pair of `(longitude, latitude)`. [`redis::geo::Coord`][1]","highlight_start":1,"highlight_end":86},{"text":"    /// can be used to convert these value in a struct.","highlight_start":1,"highlight_end":56},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// [1]: ./geo/struct.Coord.html","highlight_start":1,"highlight_end":37},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// # Example","highlight_start":1,"highlight_end":18},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":23},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":44},{"text":"    /// use redis::geo::Coord;","highlight_start":1,"highlight_end":31},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// fn get_position(con: &mut redis::Connection) {","highlight_start":1,"highlight_end":55},{"text":"    ///     let x: RedisResult<Vec<Vec<f64>>> = con.geo_pos(\"my_gis\", &[\"Palermo\", \"Catania\"]);","highlight_start":1,"highlight_end":96},{"text":"    ///     // x is [ [ 13.361389, 38.115556 ], [ 15.087269, 37.502669 ] ];","highlight_start":1,"highlight_end":76},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    ///     let x: Vec<Coord<f64>> = con.geo_pos(\"my_gis\", \"Palermo\").unwrap();","highlight_start":1,"highlight_end":80},{"text":"    ///     // x[0].longitude is 13.361389","highlight_start":1,"highlight_end":43},{"text":"    ///     // x[0].latitude is 38.115556","highlight_start":1,"highlight_end":42},{"text":"    /// }","highlight_start":1,"highlight_end":10},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":35},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":58},{"text":"    fn geo_pos<K: ToRedisArgs, M: ToRedisArgs>(key: K, members: M) {","highlight_start":1,"highlight_end":69},{"text":"        cmd(\"GEOPOS\").arg(key).arg(members)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Return the members of a sorted set populated with geospatial information","highlight_start":1,"highlight_end":81},{"text":"    /// using [`geo_add`](#method.geo_add), which are within the borders of the area","highlight_start":1,"highlight_end":85},{"text":"    /// specified with the center location and the maximum distance from the center","highlight_start":1,"highlight_end":84},{"text":"    /// (the radius).","highlight_start":1,"highlight_end":22},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Every item in the result can be read with [`redis::geo::RadiusSearchResult`][1],","highlight_start":1,"highlight_end":89},{"text":"    /// which support the multiple formats returned by `GEORADIUS`.","highlight_start":1,"highlight_end":68},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// [1]: ./geo/struct.RadiusSearchResult.html","highlight_start":1,"highlight_end":50},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```rust,no_run","highlight_start":1,"highlight_end":23},{"text":"    /// use redis::{Commands, RedisResult};","highlight_start":1,"highlight_end":44},{"text":"    /// use redis::geo::{RadiusOptions, RadiusSearchResult, RadiusOrder, Unit};","highlight_start":1,"highlight_end":80},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// fn radius(con: &mut redis::Connection) -> Vec<RadiusSearchResult> {","highlight_start":1,"highlight_end":76},{"text":"    ///     let opts = RadiusOptions::default().with_dist().order(RadiusOrder::Asc);","highlight_start":1,"highlight_end":85},{"text":"    ///     con.geo_radius(\"my_gis\", 15.90, 37.21, 51.39, Unit::Kilometers, opts).unwrap()","highlight_start":1,"highlight_end":91},{"text":"    /// }","highlight_start":1,"highlight_end":10},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":35},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":58},{"text":"    fn geo_radius<K: ToRedisArgs>(","highlight_start":1,"highlight_end":35},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        longitude: f64,","highlight_start":1,"highlight_end":24},{"text":"        latitude: f64,","highlight_start":1,"highlight_end":23},{"text":"        radius: f64,","highlight_start":1,"highlight_end":21},{"text":"        unit: geo::Unit,","highlight_start":1,"highlight_end":25},{"text":"        options: geo::RadiusOptions","highlight_start":1,"highlight_end":36},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"GEORADIUS\")","highlight_start":1,"highlight_end":25},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(longitude)","highlight_start":1,"highlight_end":28},{"text":"            .arg(latitude)","highlight_start":1,"highlight_end":27},{"text":"            .arg(radius)","highlight_start":1,"highlight_end":25},{"text":"            .arg(unit)","highlight_start":1,"highlight_end":23},{"text":"            .arg(options)","highlight_start":1,"highlight_end":26},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Retrieve members selected by distance with the center of `member`. The","highlight_start":1,"highlight_end":79},{"text":"    /// member itself is always contained in the results.","highlight_start":1,"highlight_end":58},{"text":"    #[cfg(feature = \"geospatial\")]","highlight_start":1,"highlight_end":35},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"geospatial\")))]","highlight_start":1,"highlight_end":58},{"text":"    fn geo_radius_by_member<K: ToRedisArgs, M: ToRedisArgs>(","highlight_start":1,"highlight_end":61},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        member: M,","highlight_start":1,"highlight_end":19},{"text":"        radius: f64,","highlight_start":1,"highlight_end":21},{"text":"        unit: geo::Unit,","highlight_start":1,"highlight_end":25},{"text":"        options: geo::RadiusOptions","highlight_start":1,"highlight_end":36},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"GEORADIUSBYMEMBER\")","highlight_start":1,"highlight_end":33},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(member)","highlight_start":1,"highlight_end":25},{"text":"            .arg(radius)","highlight_start":1,"highlight_end":25},{"text":"            .arg(unit)","highlight_start":1,"highlight_end":23},{"text":"            .arg(options)","highlight_start":1,"highlight_end":26},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    //","highlight_start":1,"highlight_end":7},{"text":"    // streams commands","highlight_start":1,"highlight_end":24},{"text":"    //","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Ack pending stream messages checked out by a consumer.","highlight_start":1,"highlight_end":63},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XACK <key> <group> <id> <id> ... <id>","highlight_start":1,"highlight_end":46},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xack<K: ToRedisArgs, G: ToRedisArgs, I: ToRedisArgs>(","highlight_start":1,"highlight_end":61},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        ids: &'a [I]) {","highlight_start":1,"highlight_end":24},{"text":"        cmd(\"XACK\")","highlight_start":1,"highlight_end":20},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(ids)","highlight_start":1,"highlight_end":22},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add a stream message by `key`. Use `*` as the `id` for the current timestamp.","highlight_start":1,"highlight_end":86},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XADD key <ID or *> [field value] [field value] ...","highlight_start":1,"highlight_end":59},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xadd<K: ToRedisArgs, ID: ToRedisArgs, F: ToRedisArgs, V: ToRedisArgs>(","highlight_start":1,"highlight_end":78},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        id: ID,","highlight_start":1,"highlight_end":16},{"text":"        items: &'a [(F, V)]","highlight_start":1,"highlight_end":28},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XADD\").arg(key).arg(id).arg(items)","highlight_start":1,"highlight_end":48},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// BTreeMap variant for adding a stream message by `key`.","highlight_start":1,"highlight_end":63},{"text":"    /// Use `*` as the `id` for the current timestamp.","highlight_start":1,"highlight_end":55},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XADD key <ID or *> [rust BTreeMap] ...","highlight_start":1,"highlight_end":47},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xadd_map<K: ToRedisArgs, ID: ToRedisArgs, BTM: ToRedisArgs>(","highlight_start":1,"highlight_end":68},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        id: ID,","highlight_start":1,"highlight_end":16},{"text":"        map: BTM","highlight_start":1,"highlight_end":17},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XADD\").arg(key).arg(id).arg(map)","highlight_start":1,"highlight_end":46},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Add a stream message while capping the stream at a maxlength.","highlight_start":1,"highlight_end":70},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XADD key [MAXLEN [~|=] <count>] <ID or *> [field value] [field value] ...","highlight_start":1,"highlight_end":82},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xadd_maxlen<","highlight_start":1,"highlight_end":20},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        ID: ToRedisArgs,","highlight_start":1,"highlight_end":25},{"text":"        F: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        V: ToRedisArgs","highlight_start":1,"highlight_end":23},{"text":"    >(","highlight_start":1,"highlight_end":7},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        maxlen: streams::StreamMaxlen,","highlight_start":1,"highlight_end":39},{"text":"        id: ID,","highlight_start":1,"highlight_end":16},{"text":"        items: &'a [(F, V)]","highlight_start":1,"highlight_end":28},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XADD\")","highlight_start":1,"highlight_end":20},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(maxlen)","highlight_start":1,"highlight_end":25},{"text":"            .arg(id)","highlight_start":1,"highlight_end":21},{"text":"            .arg(items)","highlight_start":1,"highlight_end":24},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// BTreeMap variant for adding a stream message while capping the stream at a maxlength.","highlight_start":1,"highlight_end":94},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XADD key [MAXLEN [~|=] <count>] <ID or *> [rust BTreeMap] ...","highlight_start":1,"highlight_end":70},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xadd_maxlen_map<K: ToRedisArgs, ID: ToRedisArgs, BTM: ToRedisArgs>(","highlight_start":1,"highlight_end":75},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        maxlen: streams::StreamMaxlen,","highlight_start":1,"highlight_end":39},{"text":"        id: ID,","highlight_start":1,"highlight_end":16},{"text":"        map: BTM","highlight_start":1,"highlight_end":17},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XADD\")","highlight_start":1,"highlight_end":20},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(maxlen)","highlight_start":1,"highlight_end":25},{"text":"            .arg(id)","highlight_start":1,"highlight_end":21},{"text":"            .arg(map)","highlight_start":1,"highlight_end":22},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Claim pending, unacked messages, after some period of time,","highlight_start":1,"highlight_end":68},{"text":"    /// currently checked out by another consumer.","highlight_start":1,"highlight_end":51},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// This method only accepts the must-have arguments for claiming messages.","highlight_start":1,"highlight_end":80},{"text":"    /// If optional arguments are required, see `xclaim_options` below.","highlight_start":1,"highlight_end":72},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XCLAIM <key> <group> <consumer> <min-idle-time> [<ID-1> <ID-2>]","highlight_start":1,"highlight_end":72},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xclaim<K: ToRedisArgs, G: ToRedisArgs, C: ToRedisArgs, MIT: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":98},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        consumer: C,","highlight_start":1,"highlight_end":21},{"text":"        min_idle_time: MIT,","highlight_start":1,"highlight_end":28},{"text":"        ids: &'a [ID]","highlight_start":1,"highlight_end":22},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XCLAIM\")","highlight_start":1,"highlight_end":22},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":27},{"text":"            .arg(min_idle_time)","highlight_start":1,"highlight_end":32},{"text":"            .arg(ids)","highlight_start":1,"highlight_end":22},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the optional arguments version for claiming unacked, pending messages","highlight_start":1,"highlight_end":86},{"text":"    /// currently checked out by another consumer.","highlight_start":1,"highlight_end":51},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```no_run","highlight_start":1,"highlight_end":18},{"text":"    /// use redis::{Connection,Commands,RedisResult};","highlight_start":1,"highlight_end":54},{"text":"    /// use redis::streams::{StreamClaimOptions,StreamClaimReply};","highlight_start":1,"highlight_end":67},{"text":"    /// let client = redis::Client::open(\"redis://127.0.0.1/0\").unwrap();","highlight_start":1,"highlight_end":74},{"text":"    /// let mut con = client.get_connection().unwrap();","highlight_start":1,"highlight_end":56},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// // Claim all pending messages for key \"k1\",","highlight_start":1,"highlight_end":52},{"text":"    /// // from group \"g1\", checked out by consumer \"c1\"","highlight_start":1,"highlight_end":57},{"text":"    /// // for 10ms with RETRYCOUNT 2 and FORCE","highlight_start":1,"highlight_end":48},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// let opts = StreamClaimOptions::default()","highlight_start":1,"highlight_end":49},{"text":"    ///     .with_force()","highlight_start":1,"highlight_end":26},{"text":"    ///     .retry(2);","highlight_start":1,"highlight_end":23},{"text":"    /// let results: RedisResult<StreamClaimReply> =","highlight_start":1,"highlight_end":53},{"text":"    ///     con.xclaim_options(\"k1\", \"g1\", \"c1\", 10, &[\"0\"], opts);","highlight_start":1,"highlight_end":68},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// // All optional arguments return a `Result<StreamClaimReply>` with one exception:","highlight_start":1,"highlight_end":90},{"text":"    /// // Passing JUSTID returns only the message `id` and omits the HashMap for each message.","highlight_start":1,"highlight_end":96},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// let opts = StreamClaimOptions::default()","highlight_start":1,"highlight_end":49},{"text":"    ///     .with_justid();","highlight_start":1,"highlight_end":28},{"text":"    /// let results: RedisResult<Vec<String>> =","highlight_start":1,"highlight_end":48},{"text":"    ///     con.xclaim_options(\"k1\", \"g1\", \"c1\", 10, &[\"0\"], opts);","highlight_start":1,"highlight_end":68},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XCLAIM <key> <group> <consumer> <min-idle-time> <ID-1> <ID-2>","highlight_start":1,"highlight_end":70},{"text":"    ///     [IDLE <milliseconds>] [TIME <mstime>] [RETRYCOUNT <count>]","highlight_start":1,"highlight_end":71},{"text":"    ///     [FORCE] [JUSTID]","highlight_start":1,"highlight_end":29},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xclaim_options<","highlight_start":1,"highlight_end":23},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        C: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        MIT: ToRedisArgs,","highlight_start":1,"highlight_end":26},{"text":"        ID: ToRedisArgs","highlight_start":1,"highlight_end":24},{"text":"    >(","highlight_start":1,"highlight_end":7},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        consumer: C,","highlight_start":1,"highlight_end":21},{"text":"        min_idle_time: MIT,","highlight_start":1,"highlight_end":28},{"text":"        ids: &'a [ID],","highlight_start":1,"highlight_end":23},{"text":"        options: streams::StreamClaimOptions","highlight_start":1,"highlight_end":45},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XCLAIM\")","highlight_start":1,"highlight_end":22},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":27},{"text":"            .arg(min_idle_time)","highlight_start":1,"highlight_end":32},{"text":"            .arg(ids)","highlight_start":1,"highlight_end":22},{"text":"            .arg(options)","highlight_start":1,"highlight_end":26},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Deletes a list of `id`s for a given stream `key`.","highlight_start":1,"highlight_end":58},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XDEL <key> [<ID1> <ID2> ... <IDN>]","highlight_start":1,"highlight_end":43},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xdel<K: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":46},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        ids: &'a [ID]","highlight_start":1,"highlight_end":22},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XDEL\").arg(key).arg(ids)","highlight_start":1,"highlight_end":38},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This command is used for creating a consumer `group`. It expects the stream key","highlight_start":1,"highlight_end":88},{"text":"    /// to already exist. Otherwise, use `xgroup_create_mkstream` if it doesn't.","highlight_start":1,"highlight_end":81},{"text":"    /// The `id` is the starting message id all consumers should read from. Use `$` If you want","highlight_start":1,"highlight_end":96},{"text":"    /// all consumers to read from the last message added to stream.","highlight_start":1,"highlight_end":69},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XGROUP CREATE <key> <groupname> <id or $>","highlight_start":1,"highlight_end":50},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xgroup_create<K: ToRedisArgs, G: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":71},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        id: ID","highlight_start":1,"highlight_end":15},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":22},{"text":"            .arg(\"CREATE\")","highlight_start":1,"highlight_end":27},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(id)","highlight_start":1,"highlight_end":21},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the alternate version for creating a consumer `group`","highlight_start":1,"highlight_end":70},{"text":"    /// which makes the stream if it doesn't exist.","highlight_start":1,"highlight_end":52},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XGROUP CREATE <key> <groupname> <id or $> [MKSTREAM]","highlight_start":1,"highlight_end":61},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xgroup_create_mkstream<","highlight_start":1,"highlight_end":31},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        ID: ToRedisArgs","highlight_start":1,"highlight_end":24},{"text":"    >(","highlight_start":1,"highlight_end":7},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        id: ID","highlight_start":1,"highlight_end":15},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":22},{"text":"            .arg(\"CREATE\")","highlight_start":1,"highlight_end":27},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(id)","highlight_start":1,"highlight_end":21},{"text":"            .arg(\"MKSTREAM\")","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Alter which `id` you want consumers to begin reading from an existing","highlight_start":1,"highlight_end":78},{"text":"    /// consumer `group`.","highlight_start":1,"highlight_end":26},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XGROUP SETID <key> <groupname> <id or $>","highlight_start":1,"highlight_end":49},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xgroup_setid<K: ToRedisArgs, G: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":70},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        id: ID","highlight_start":1,"highlight_end":15},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":22},{"text":"            .arg(\"SETID\")","highlight_start":1,"highlight_end":26},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(id)","highlight_start":1,"highlight_end":21},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Destroy an existing consumer `group` for a given stream `key`","highlight_start":1,"highlight_end":70},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XGROUP SETID <key> <groupname> <id or $>","highlight_start":1,"highlight_end":49},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xgroup_destroy<K: ToRedisArgs, G: ToRedisArgs>(","highlight_start":1,"highlight_end":55},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G","highlight_start":1,"highlight_end":17},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XGROUP\").arg(\"DESTROY\").arg(key).arg(group)","highlight_start":1,"highlight_end":57},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This deletes a `consumer` from an existing consumer `group`","highlight_start":1,"highlight_end":68},{"text":"    /// for given stream `key.","highlight_start":1,"highlight_end":31},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XGROUP DELCONSUMER <key> <groupname> <consumername>","highlight_start":1,"highlight_end":60},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xgroup_delconsumer<K: ToRedisArgs, G: ToRedisArgs, C: ToRedisArgs>(","highlight_start":1,"highlight_end":75},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        consumer: C","highlight_start":1,"highlight_end":20},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XGROUP\")","highlight_start":1,"highlight_end":22},{"text":"            .arg(\"DELCONSUMER\")","highlight_start":1,"highlight_end":32},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":27},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This returns all info details about","highlight_start":1,"highlight_end":44},{"text":"    /// which consumers have read messages for given consumer `group`.","highlight_start":1,"highlight_end":71},{"text":"    /// Take note of the StreamInfoConsumersReply return type.","highlight_start":1,"highlight_end":63},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// *It's possible this return value might not contain new fields","highlight_start":1,"highlight_end":70},{"text":"    /// added by Redis in future versions.*","highlight_start":1,"highlight_end":44},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XINFO CONSUMERS <key> <group>","highlight_start":1,"highlight_end":38},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xinfo_consumers<K: ToRedisArgs, G: ToRedisArgs>(","highlight_start":1,"highlight_end":56},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G","highlight_start":1,"highlight_end":17},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XINFO\")","highlight_start":1,"highlight_end":21},{"text":"            .arg(\"CONSUMERS\")","highlight_start":1,"highlight_end":30},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns all consumer `group`s created for a given stream `key`.","highlight_start":1,"highlight_end":72},{"text":"    /// Take note of the StreamInfoGroupsReply return type.","highlight_start":1,"highlight_end":60},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// *It's possible this return value might not contain new fields","highlight_start":1,"highlight_end":70},{"text":"    /// added by Redis in future versions.*","highlight_start":1,"highlight_end":44},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XINFO GROUPS <key>","highlight_start":1,"highlight_end":27},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xinfo_groups<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":46},{"text":"        cmd(\"XINFO\").arg(\"GROUPS\").arg(key)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns info about high-level stream details","highlight_start":1,"highlight_end":53},{"text":"    /// (first & last message `id`, length, number of groups, etc.)","highlight_start":1,"highlight_end":68},{"text":"    /// Take note of the StreamInfoStreamReply return type.","highlight_start":1,"highlight_end":60},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// *It's possible this return value might not contain new fields","highlight_start":1,"highlight_end":70},{"text":"    /// added by Redis in future versions.*","highlight_start":1,"highlight_end":44},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XINFO STREAM <key>","highlight_start":1,"highlight_end":27},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xinfo_stream<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":46},{"text":"        cmd(\"XINFO\").arg(\"STREAM\").arg(key)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns the number of messages for a given stream `key`.","highlight_start":1,"highlight_end":65},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XLEN <key>","highlight_start":1,"highlight_end":19},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xlen<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":38},{"text":"        cmd(\"XLEN\").arg(key)","highlight_start":1,"highlight_end":29},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is a basic version of making XPENDING command calls which only","highlight_start":1,"highlight_end":76},{"text":"    /// passes a stream `key` and consumer `group` and it","highlight_start":1,"highlight_end":58},{"text":"    /// returns details about which consumers have pending messages","highlight_start":1,"highlight_end":68},{"text":"    /// that haven't been acked.","highlight_start":1,"highlight_end":33},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// You can use this method along with","highlight_start":1,"highlight_end":43},{"text":"    /// `xclaim` or `xclaim_options` for determining which messages","highlight_start":1,"highlight_end":68},{"text":"    /// need to be retried.","highlight_start":1,"highlight_end":28},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Take note of the StreamPendingReply return type.","highlight_start":1,"highlight_end":57},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XPENDING <key> <group> [<start> <stop> <count> [<consumer>]]","highlight_start":1,"highlight_end":69},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xpending<K: ToRedisArgs, G: ToRedisArgs>(","highlight_start":1,"highlight_end":49},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G","highlight_start":1,"highlight_end":17},{"text":"    )  {","highlight_start":1,"highlight_end":9},{"text":"        cmd(\"XPENDING\").arg(key).arg(group)","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This XPENDING version returns a list of all messages over the range.","highlight_start":1,"highlight_end":77},{"text":"    /// You can use this for paginating pending messages (but without the message HashMap).","highlight_start":1,"highlight_end":92},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Start and end follow the same rules `xrange` args. Set start to `-`","highlight_start":1,"highlight_end":76},{"text":"    /// and end to `+` for the entire stream.","highlight_start":1,"highlight_end":46},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Take note of the StreamPendingCountReply return type.","highlight_start":1,"highlight_end":62},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XPENDING <key> <group> <start> <stop> <count>","highlight_start":1,"highlight_end":54},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xpending_count<","highlight_start":1,"highlight_end":23},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        S: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        E: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        C: ToRedisArgs","highlight_start":1,"highlight_end":23},{"text":"    >(","highlight_start":1,"highlight_end":7},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        start: S,","highlight_start":1,"highlight_end":18},{"text":"        end: E,","highlight_start":1,"highlight_end":16},{"text":"        count: C","highlight_start":1,"highlight_end":17},{"text":"    )  {","highlight_start":1,"highlight_end":9},{"text":"        cmd(\"XPENDING\")","highlight_start":1,"highlight_end":24},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(start)","highlight_start":1,"highlight_end":24},{"text":"            .arg(end)","highlight_start":1,"highlight_end":22},{"text":"            .arg(count)","highlight_start":1,"highlight_end":24},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// An alternate version of `xpending_count` which filters by `consumer` name.","highlight_start":1,"highlight_end":83},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Start and end follow the same rules `xrange` args. Set start to `-`","highlight_start":1,"highlight_end":76},{"text":"    /// and end to `+` for the entire stream.","highlight_start":1,"highlight_end":46},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Take note of the StreamPendingCountReply return type.","highlight_start":1,"highlight_end":62},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XPENDING <key> <group> <start> <stop> <count> <consumer>","highlight_start":1,"highlight_end":65},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xpending_consumer_count<","highlight_start":1,"highlight_end":32},{"text":"        K: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        G: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        S: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        E: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        C: ToRedisArgs,","highlight_start":1,"highlight_end":24},{"text":"        CN: ToRedisArgs","highlight_start":1,"highlight_end":24},{"text":"    >(","highlight_start":1,"highlight_end":7},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        group: G,","highlight_start":1,"highlight_end":18},{"text":"        start: S,","highlight_start":1,"highlight_end":18},{"text":"        end: E,","highlight_start":1,"highlight_end":16},{"text":"        count: C,","highlight_start":1,"highlight_end":18},{"text":"        consumer: CN","highlight_start":1,"highlight_end":21},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XPENDING\")","highlight_start":1,"highlight_end":24},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(group)","highlight_start":1,"highlight_end":24},{"text":"            .arg(start)","highlight_start":1,"highlight_end":24},{"text":"            .arg(end)","highlight_start":1,"highlight_end":22},{"text":"            .arg(count)","highlight_start":1,"highlight_end":24},{"text":"            .arg(consumer)","highlight_start":1,"highlight_end":27},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Returns a range of messages in a given stream `key`.","highlight_start":1,"highlight_end":61},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Set `start` to `-` to begin at the first message.","highlight_start":1,"highlight_end":58},{"text":"    /// Set `end` to `+` to end the most recent message.","highlight_start":1,"highlight_end":57},{"text":"    /// You can pass message `id` to both `start` and `end`.","highlight_start":1,"highlight_end":61},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// Take note of the StreamRangeReply return type.","highlight_start":1,"highlight_end":55},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XRANGE key start end","highlight_start":1,"highlight_end":29},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xrange<K: ToRedisArgs, S: ToRedisArgs, E: ToRedisArgs>(","highlight_start":1,"highlight_end":63},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        start: S,","highlight_start":1,"highlight_end":18},{"text":"        end: E","highlight_start":1,"highlight_end":15},{"text":"    )  {","highlight_start":1,"highlight_end":9},{"text":"        cmd(\"XRANGE\").arg(key).arg(start).arg(end)","highlight_start":1,"highlight_end":51},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// A helper method for automatically returning all messages in a stream by `key`.","highlight_start":1,"highlight_end":87},{"text":"    /// **Use with caution!**","highlight_start":1,"highlight_end":30},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XRANGE key - +","highlight_start":1,"highlight_end":23},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xrange_all<K: ToRedisArgs>(key: K)  {","highlight_start":1,"highlight_end":45},{"text":"        cmd(\"XRANGE\").arg(key).arg(\"-\").arg(\"+\")","highlight_start":1,"highlight_end":49},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// A method for paginating a stream by `key`.","highlight_start":1,"highlight_end":51},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XRANGE key start end [COUNT <n>]","highlight_start":1,"highlight_end":41},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xrange_count<K: ToRedisArgs, S: ToRedisArgs, E: ToRedisArgs, C: ToRedisArgs>(","highlight_start":1,"highlight_end":85},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        start: S,","highlight_start":1,"highlight_end":18},{"text":"        end: E,","highlight_start":1,"highlight_end":16},{"text":"        count: C","highlight_start":1,"highlight_end":17},{"text":"    )  {","highlight_start":1,"highlight_end":9},{"text":"        cmd(\"XRANGE\")","highlight_start":1,"highlight_end":22},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(start)","highlight_start":1,"highlight_end":24},{"text":"            .arg(end)","highlight_start":1,"highlight_end":22},{"text":"            .arg(\"COUNT\")","highlight_start":1,"highlight_end":26},{"text":"            .arg(count)","highlight_start":1,"highlight_end":24},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Read a list of `id`s for each stream `key`.","highlight_start":1,"highlight_end":52},{"text":"    /// This is the basic form of reading streams.","highlight_start":1,"highlight_end":51},{"text":"    /// For more advanced control, like blocking, limiting, or reading by consumer `group`,","highlight_start":1,"highlight_end":92},{"text":"    /// see `xread_options`.","highlight_start":1,"highlight_end":29},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XREAD STREAMS key_1 key_2 ... key_N ID_1 ID_2 ... ID_N","highlight_start":1,"highlight_end":63},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xread<K: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":47},{"text":"        keys: &'a [K],","highlight_start":1,"highlight_end":23},{"text":"        ids: &'a [ID]","highlight_start":1,"highlight_end":22},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XREAD\").arg(\"STREAMS\").arg(keys).arg(ids)","highlight_start":1,"highlight_end":55},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This method handles setting optional arguments for","highlight_start":1,"highlight_end":59},{"text":"    /// `XREAD` or `XREADGROUP` Redis commands.","highlight_start":1,"highlight_end":48},{"text":"    /// ```no_run","highlight_start":1,"highlight_end":18},{"text":"    /// use redis::{Connection,RedisResult,Commands};","highlight_start":1,"highlight_end":54},{"text":"    /// use redis::streams::{StreamReadOptions,StreamReadReply};","highlight_start":1,"highlight_end":65},{"text":"    /// let client = redis::Client::open(\"redis://127.0.0.1/0\").unwrap();","highlight_start":1,"highlight_end":74},{"text":"    /// let mut con = client.get_connection().unwrap();","highlight_start":1,"highlight_end":56},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// // Read 10 messages from the start of the stream,","highlight_start":1,"highlight_end":58},{"text":"    /// // without registering as a consumer group.","highlight_start":1,"highlight_end":52},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// let opts = StreamReadOptions::default()","highlight_start":1,"highlight_end":48},{"text":"    ///     .count(10);","highlight_start":1,"highlight_end":24},{"text":"    /// let results: RedisResult<StreamReadReply> =","highlight_start":1,"highlight_end":52},{"text":"    ///     con.xread_options(&[\"k1\"], &[\"0\"], &opts);","highlight_start":1,"highlight_end":55},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// // Read all undelivered messages for a given","highlight_start":1,"highlight_end":53},{"text":"    /// // consumer group. Be advised: the consumer group must already","highlight_start":1,"highlight_end":71},{"text":"    /// // exist before making this call. Also note: we're passing","highlight_start":1,"highlight_end":67},{"text":"    /// // '>' as the id here, which means all undelivered messages.","highlight_start":1,"highlight_end":69},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// let opts = StreamReadOptions::default()","highlight_start":1,"highlight_end":48},{"text":"    ///     .group(\"group-1\", \"consumer-1\");","highlight_start":1,"highlight_end":45},{"text":"    /// let results: RedisResult<StreamReadReply> =","highlight_start":1,"highlight_end":52},{"text":"    ///     con.xread_options(&[\"k1\"], &[\">\"], &opts);","highlight_start":1,"highlight_end":55},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XREAD [BLOCK <milliseconds>] [COUNT <count>]","highlight_start":1,"highlight_end":53},{"text":"    ///     STREAMS key_1 key_2 ... key_N","highlight_start":1,"highlight_end":42},{"text":"    ///     ID_1 ID_2 ... ID_N","highlight_start":1,"highlight_end":31},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// XREADGROUP [GROUP group-name consumer-name] [BLOCK <milliseconds>] [COUNT <count>] [NOACK] ","highlight_start":1,"highlight_end":100},{"text":"    ///     STREAMS key_1 key_2 ... key_N","highlight_start":1,"highlight_end":42},{"text":"    ///     ID_1 ID_2 ... ID_N","highlight_start":1,"highlight_end":31},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xread_options<K: ToRedisArgs, ID: ToRedisArgs>(","highlight_start":1,"highlight_end":55},{"text":"        keys: &'a [K],","highlight_start":1,"highlight_end":23},{"text":"        ids: &'a [ID],","highlight_start":1,"highlight_end":23},{"text":"        options: &'a streams::StreamReadOptions","highlight_start":1,"highlight_end":48},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(if options.read_only() {","highlight_start":1,"highlight_end":37},{"text":"            \"XREAD\"","highlight_start":1,"highlight_end":20},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            \"XREADGROUP\"","highlight_start":1,"highlight_end":25},{"text":"        })","highlight_start":1,"highlight_end":11},{"text":"        .arg(options)","highlight_start":1,"highlight_end":22},{"text":"        .arg(\"STREAMS\")","highlight_start":1,"highlight_end":24},{"text":"        .arg(keys)","highlight_start":1,"highlight_end":19},{"text":"        .arg(ids)","highlight_start":1,"highlight_end":18},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the reverse version of `xrange`.","highlight_start":1,"highlight_end":49},{"text":"    /// The same rules apply for `start` and `end` here.","highlight_start":1,"highlight_end":57},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XREVRANGE key end start","highlight_start":1,"highlight_end":32},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xrevrange<K: ToRedisArgs, E: ToRedisArgs, S: ToRedisArgs>(","highlight_start":1,"highlight_end":66},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        end: E,","highlight_start":1,"highlight_end":16},{"text":"        start: S","highlight_start":1,"highlight_end":17},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XREVRANGE\").arg(key).arg(end).arg(start)","highlight_start":1,"highlight_end":54},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the reverse version of `xrange_all`.","highlight_start":1,"highlight_end":53},{"text":"    /// The same rules apply for `start` and `end` here.","highlight_start":1,"highlight_end":57},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XREVRANGE key + -","highlight_start":1,"highlight_end":26},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    fn xrevrange_all<K: ToRedisArgs>(key: K) {","highlight_start":1,"highlight_end":47},{"text":"        cmd(\"XREVRANGE\").arg(key).arg(\"+\").arg(\"-\")","highlight_start":1,"highlight_end":52},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// This is the reverse version of `xrange_count`.","highlight_start":1,"highlight_end":55},{"text":"    /// The same rules apply for `start` and `end` here.","highlight_start":1,"highlight_end":57},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XREVRANGE key end start [COUNT <n>]","highlight_start":1,"highlight_end":44},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xrevrange_count<K: ToRedisArgs, E: ToRedisArgs, S: ToRedisArgs, C: ToRedisArgs>(","highlight_start":1,"highlight_end":88},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        end: E,","highlight_start":1,"highlight_end":16},{"text":"        start: S,","highlight_start":1,"highlight_end":18},{"text":"        count: C","highlight_start":1,"highlight_end":17},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XREVRANGE\")","highlight_start":1,"highlight_end":25},{"text":"            .arg(key)","highlight_start":1,"highlight_end":22},{"text":"            .arg(end)","highlight_start":1,"highlight_end":22},{"text":"            .arg(start)","highlight_start":1,"highlight_end":24},{"text":"            .arg(\"COUNT\")","highlight_start":1,"highlight_end":26},{"text":"            .arg(count)","highlight_start":1,"highlight_end":24},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Trim a stream `key` to a MAXLEN count.","highlight_start":1,"highlight_end":47},{"text":"    ///","highlight_start":1,"highlight_end":8},{"text":"    /// ```text","highlight_start":1,"highlight_end":16},{"text":"    /// XTRIM <key> MAXLEN [~|=] <count>  (Same as XADD MAXLEN option)","highlight_start":1,"highlight_end":71},{"text":"    /// ```","highlight_start":1,"highlight_end":12},{"text":"    #[cfg(feature = \"streams\")]","highlight_start":1,"highlight_end":32},{"text":"    #[cfg_attr(docsrs, doc(cfg(feature = \"streams\")))]","highlight_start":1,"highlight_end":55},{"text":"    fn xtrim<K: ToRedisArgs>(","highlight_start":1,"highlight_end":30},{"text":"        key: K,","highlight_start":1,"highlight_end":16},{"text":"        maxlen: streams::StreamMaxlen","highlight_start":1,"highlight_end":38},{"text":"    ) {","highlight_start":1,"highlight_end":8},{"text":"        cmd(\"XTRIM\").arg(key).arg(maxlen)","highlight_start":1,"highlight_end":42},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"implement_commands!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\macros.rs","byte_start":0,"byte_end":31,"line_start":1,"line_end":1,"column_start":1,"column_end":32,"is_primary":false,"text":[{"text":"macro_rules! implement_commands {","highlight_start":1,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `ping` found for struct `redis::aio::Connection` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils.rs:136:51\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m136\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let result: Result<String, _> = c.ping().await;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `xpending` with a similar name, but with different arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\redis-0.24.0\\src\\commands\\mod.rs:41:1\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mimplement_commands! {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    'a\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1873\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `implement_commands` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\database.rs","byte_start":5885,"byte_end":5905,"line_start":187,"line_end":187,"column_start":28,"column_end":48,"is_primary":true,"text":[{"text":"        (self.pool.size(), self.pool.num_idle())","highlight_start":28,"highlight_end":48}],"label":"expected `u32`, found `usize`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"you can convert a `usize` to a `u32` and panic if the converted value doesn't fit","code":null,"level":"help","spans":[{"file_name":"src\\database.rs","byte_start":5905,"byte_end":5905,"line_start":187,"line_end":187,"column_start":48,"column_end":48,"is_primary":true,"text":[{"text":"        (self.pool.size(), self.pool.num_idle())","highlight_start":48,"highlight_end":48}],"label":null,"suggested_replacement":".try_into().unwrap()","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\database.rs:187:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (self.pool.size(), self.pool.num_idle())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `u32`, found `usize`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can convert a `usize` to a `u32` and panic if the converted value doesn't fit\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        (self.pool.size(), self.pool.num_idle()\u001b[0m\u001b[0m\u001b[38;5;10m.try_into().unwrap()\u001b[0m\u001b[0m)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 17 previous errors; 12 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 17 previous errors; 12 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0308, E0412, E0433, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0308, E0412, E0433, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0308`.\u001b[0m\n"}
