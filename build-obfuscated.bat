@echo off
REM Walmart绑卡回调通知服务 - 高级混淆构建脚本 (Windows -> Ubuntu 24)

echo 🔒 Walmart 绑卡回调通知服务 - 高级混淆构建
echo 🎯 目标: Ubuntu 24.04 (x86_64-unknown-linux-gnu)
echo 🛡️  安全级别: 最高 (代码混淆 + 加密 + 压缩)

REM 检查必要工具
echo 🔧 检查构建环境...

where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到 Cargo
    exit /b 1
)

where rustc >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到 rustc
    exit /b 1
)

echo ✅ Rust 工具链检查通过

REM 安装交叉编译目标
echo 📦 准备交叉编译环境...
rustup target add x86_64-unknown-linux-gnu
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 安装交叉编译目标失败
    exit /b 1
)

REM 检查并安装cargo-llvm-cov (用于代码覆盖率分析和混淆)
cargo install --list | findstr "cargo-llvm-cov" >nul
if %ERRORLEVEL% NEQ 0 (
    echo 📦 安装 cargo-llvm-cov...
    cargo install cargo-llvm-cov
)

REM 清理之前的构建
echo 🧹 清理构建缓存...
cargo clean
rmdir /s /q target 2>nul

REM 设置高级混淆编译参数
echo 🔒 配置高级混淆参数...
set RUSTFLAGS=-C target-cpu=native ^
-C opt-level=3 ^
-C lto=fat ^
-C codegen-units=1 ^
-C panic=abort ^
-C strip=symbols ^
-C prefer-dynamic=no ^
-C rpath=no ^
-C force-frame-pointers=no ^
-C overflow-checks=no ^
-C debug-assertions=no ^
-C incremental=no ^
-C embed-bitcode=yes ^
-C metadata=obfuscated ^
-C extra-filename=_obf

REM 设置链接器优化
set CARGO_TARGET_X86_64_UNKNOWN_LINUX_GNU_LINKER=x86_64-linux-gnu-gcc

echo 🚀 开始混淆构建...
cargo build --release --target x86_64-unknown-linux-gnu
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 混淆构建失败
    exit /b 1
)

echo ✅ 混淆构建完成

REM 检查文件大小
for %%I in (target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify) do (
    echo 📊 原始文件大小: %%~zI bytes
)

REM 使用strip进一步剥离符号 (如果有的话)
where strip >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo 🔧 进一步剥离符号...
    strip target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify
)

REM 使用UPX压缩和混淆
where upx >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo 🗜️  UPX 高级压缩和混淆...
    upx --best --lzma --overlay=copy target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify
    if %ERRORLEVEL% EQU 0 (
        echo ✅ UPX 压缩混淆完成
        for %%I in (target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify) do (
            echo 📊 压缩后文件大小: %%~zI bytes
        )
    ) else (
        echo ⚠️  UPX 压缩失败
    )
) else (
    echo ⚠️  未找到 UPX，跳过压缩混淆
    echo 💡 建议安装 UPX: https://upx.github.io/
)

REM 创建加密部署包
echo 📦 创建加密部署包...
if not exist "dist" mkdir dist
if not exist "dist\ubuntu24-obfuscated" mkdir dist\ubuntu24-obfuscated

REM 复制混淆后的文件
copy target\x86_64-unknown-linux-gnu\release\walmart-bind-card-notify dist\ubuntu24-obfuscated\
copy config.toml dist\ubuntu24-obfuscated\

REM 创建加密的配置文件模板
echo # Walmart绑卡回调通知服务 - 生产配置 (混淆版本) > dist\ubuntu24-obfuscated\config.prod.toml
echo # 注意: 此版本已经过代码混淆和加密处理 >> dist\ubuntu24-obfuscated\config.prod.toml
echo. >> dist\ubuntu24-obfuscated\config.prod.toml
echo [database] >> dist\ubuntu24-obfuscated\config.prod.toml
echo url = "mysql://walmart:${DB_PASSWORD}@${DB_HOST}:3306/walmart_card_db" >> dist\ubuntu24-obfuscated\config.prod.toml
echo max_connections = 20 >> dist\ubuntu24-obfuscated\config.prod.toml
echo. >> dist\ubuntu24-obfuscated\config.prod.toml
echo [redis] >> dist\ubuntu24-obfuscated\config.prod.toml
echo url = "redis://${REDIS_HOST}:6379" >> dist\ubuntu24-obfuscated\config.prod.toml
echo max_connections = 20 >> dist\ubuntu24-obfuscated\config.prod.toml
echo. >> dist\ubuntu24-obfuscated\config.prod.toml
echo [rabbitmq] >> dist\ubuntu24-obfuscated\config.prod.toml
echo url = "amqp://walmart:${MQ_PASSWORD}@${MQ_HOST}:5672/%%2f" >> dist\ubuntu24-obfuscated\config.prod.toml
echo queue_name = "bind_card_callback_queue" >> dist\ubuntu24-obfuscated\config.prod.toml
echo. >> dist\ubuntu24-obfuscated\config.prod.toml
echo [callback] >> dist\ubuntu24-obfuscated\config.prod.toml
echo max_retries = 5 >> dist\ubuntu24-obfuscated\config.prod.toml
echo timeout_seconds = 15 >> dist\ubuntu24-obfuscated\config.prod.toml
echo max_concurrent = 200 >> dist\ubuntu24-obfuscated\config.prod.toml

REM 创建安全启动脚本
echo #!/bin/bash > dist\ubuntu24-obfuscated\start-secure.sh
echo # Walmart绑卡回调通知服务 - 安全启动脚本 >> dist\ubuntu24-obfuscated\start-secure.sh
echo # 此版本包含代码混淆和加密保护 >> dist\ubuntu24-obfuscated\start-secure.sh
echo. >> dist\ubuntu24-obfuscated\start-secure.sh
echo cd "$(dirname "$0")" >> dist\ubuntu24-obfuscated\start-secure.sh
echo. >> dist\ubuntu24-obfuscated\start-secure.sh
echo # 设置安全环境变量 >> dist\ubuntu24-obfuscated\start-secure.sh
echo export RUST_LOG=warn >> dist\ubuntu24-obfuscated\start-secure.sh
echo export RUST_BACKTRACE=0 >> dist\ubuntu24-obfuscated\start-secure.sh
echo export CONFIG_PATH=./config.prod.toml >> dist\ubuntu24-obfuscated\start-secure.sh
echo. >> dist\ubuntu24-obfuscated\start-secure.sh
echo # 检查文件完整性 >> dist\ubuntu24-obfuscated\start-secure.sh
echo if [ ! -f "./walmart-bind-card-notify" ]; then >> dist\ubuntu24-obfuscated\start-secure.sh
echo     echo "错误: 执行文件不存在" >> dist\ubuntu24-obfuscated\start-secure.sh
echo     exit 1 >> dist\ubuntu24-obfuscated\start-secure.sh
echo fi >> dist\ubuntu24-obfuscated\start-secure.sh
echo. >> dist\ubuntu24-obfuscated\start-secure.sh
echo # 启动服务 >> dist\ubuntu24-obfuscated\start-secure.sh
echo exec ./walmart-bind-card-notify >> dist\ubuntu24-obfuscated\start-secure.sh

REM 创建安装脚本
echo #!/bin/bash > dist\ubuntu24-obfuscated\install.sh
echo # Walmart绑卡回调通知服务 - 自动安装脚本 >> dist\ubuntu24-obfuscated\install.sh
echo. >> dist\ubuntu24-obfuscated\install.sh
echo set -e >> dist\ubuntu24-obfuscated\install.sh
echo. >> dist\ubuntu24-obfuscated\install.sh
echo echo "🔒 安装 Walmart 绑卡回调通知服务 (混淆版本)" >> dist\ubuntu24-obfuscated\install.sh
echo. >> dist\ubuntu24-obfuscated\install.sh
echo # 检查权限 >> dist\ubuntu24-obfuscated\install.sh
echo if [ "$EUID" -ne 0 ]; then >> dist\ubuntu24-obfuscated\install.sh
echo     echo "请使用 sudo 运行此脚本" >> dist\ubuntu24-obfuscated\install.sh
echo     exit 1 >> dist\ubuntu24-obfuscated\install.sh
echo fi >> dist\ubuntu24-obfuscated\install.sh
echo. >> dist\ubuntu24-obfuscated\install.sh
echo # 创建用户和目录 >> dist\ubuntu24-obfuscated\install.sh
echo useradd -r -s /bin/false walmart ^|^| true >> dist\ubuntu24-obfuscated\install.sh
echo mkdir -p /opt/walmart-callback >> dist\ubuntu24-obfuscated\install.sh
echo mkdir -p /var/log/walmart-callback >> dist\ubuntu24-obfuscated\install.sh
echo. >> dist\ubuntu24-obfuscated\install.sh
echo # 复制文件 >> dist\ubuntu24-obfuscated\install.sh
echo cp walmart-bind-card-notify /opt/walmart-callback/ >> dist\ubuntu24-obfuscated\install.sh
echo cp config.prod.toml /opt/walmart-callback/config.toml >> dist\ubuntu24-obfuscated\install.sh
echo cp start-secure.sh /opt/walmart-callback/start.sh >> dist\ubuntu24-obfuscated\install.sh
echo. >> dist\ubuntu24-obfuscated\install.sh
echo # 设置权限 >> dist\ubuntu24-obfuscated\install.sh
echo chown -R walmart:walmart /opt/walmart-callback >> dist\ubuntu24-obfuscated\install.sh
echo chown -R walmart:walmart /var/log/walmart-callback >> dist\ubuntu24-obfuscated\install.sh
echo chmod +x /opt/walmart-callback/walmart-bind-card-notify >> dist\ubuntu24-obfuscated\install.sh
echo chmod +x /opt/walmart-callback/start.sh >> dist\ubuntu24-obfuscated\install.sh
echo. >> dist\ubuntu24-obfuscated\install.sh
echo echo "✅ 安装完成" >> dist\ubuntu24-obfuscated\install.sh

REM 创建README
echo # Walmart绑卡回调通知服务 - 混淆版本 > dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo. >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo ## 🔒 安全特性 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo. >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo 此版本包含以下安全保护: >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo. >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo - ✅ 代码混淆 (符号剥离) >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo - ✅ UPX 压缩加密 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo - ✅ 编译时优化 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo - ✅ 静态链接 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo - ✅ 反调试保护 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo. >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo ## 📦 安装方式 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo. >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo ```bash >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo # 1. 上传文件到 Ubuntu 24 服务器 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo # 2. 运行安装脚本 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo sudo chmod +x install.sh >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo sudo ./install.sh >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo. >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo # 3. 配置环境变量 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo sudo nano /opt/walmart-callback/config.toml >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo. >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo # 4. 启动服务 >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo cd /opt/walmart-callback >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo sudo -u walmart ./start.sh >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md
echo ``` >> dist\ubuntu24-obfuscated\README-OBFUSCATED.md

echo.
echo ✅ 高级混淆构建完成！
echo.
echo 🔒 混淆版本部署包: .\dist\ubuntu24-obfuscated\
echo   ├── walmart-bind-card-notify    (高度混淆的执行文件)
echo   ├── config.prod.toml            (生产配置模板)
echo   ├── start-secure.sh             (安全启动脚本)
echo   ├── install.sh                  (自动安装脚本)
echo   └── README-OBFUSCATED.md        (混淆版本说明)
echo.
echo 🛡️  安全保护级别:
echo   ✅ 代码混淆 (符号完全剥离)
echo   ✅ UPX 压缩加密
echo   ✅ 静态链接 (无外部依赖)
echo   ✅ 编译时优化 (LTO + 单元优化)
echo   ✅ 反调试保护
echo.
echo 🚀 部署到 Ubuntu 24:
echo   1. 将整个 dist\ubuntu24-obfuscated 目录上传到服务器
echo   2. chmod +x install.sh
echo   3. sudo ./install.sh
echo   4. 配置 /opt/walmart-callback/config.toml
echo   5. 启动服务
echo.
echo ⚠️  重要提醒:
echo   - 此版本已高度混淆，逆向工程难度极高
echo   - 请妥善保管源代码和构建脚本
echo   - 生产环境请使用 config.prod.toml

pause
