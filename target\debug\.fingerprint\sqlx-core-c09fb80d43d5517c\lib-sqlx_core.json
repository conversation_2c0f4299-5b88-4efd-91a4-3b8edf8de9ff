{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 2241668132362809309, "path": 5322034132811581333, "deps": [[5103565458935487, "futures_io", false, 4063379907736820283], [40386456601120721, "percent_encoding", false, 13044842269826592011], [530211389790465181, "hex", false, 6650070253009459168], [788558663644978524, "crossbeam_queue", false, 6218453006530423480], [966925859616469517, "ahash", false, 13355704893319462781], [1162433738665300155, "crc", false, 5895285644219981719], [1464803193346256239, "event_listener", false, 3577666385187124060], [1811549171721445101, "futures_channel", false, 8724166829065725316], [3150220818285335163, "url", false, 5124748645926582989], [3405817021026194662, "hashlink", false, 1985663936197831542], [3646857438214563691, "futures_intrusive", false, 15772673613726448640], [3666196340704888985, "smallvec", false, 9347890279138346553], [3712811570531045576, "byteorder", false, 12238278099557601696], [3722963349756955755, "once_cell", false, 3586476984223996149], [5986029879202738730, "log", false, 11065650482030303746], [6493259146304816786, "indexmap", false, 10013235214430247565], [7620660491849607393, "futures_core", false, 17996020517316536911], [8008191657135824715, "thiserror", false, 7926313027401658097], [8319709847752024821, "uuid", false, 6258887372946491307], [8606274917505247608, "tracing", false, 11114968597918625886], [9689903380558560274, "serde", false, 16122694801283179233], [9857275760291862238, "sha2", false, 9735827670747585305], [9897246384292347999, "chrono", false, 11374460468948023014], [10629569228670356391, "futures_util", false, 9750949516336517981], [10862088793507253106, "sqlformat", false, 15201561260145994877], [11295624341523567602, "rustls", false, 11728631060649221341], [12170264697963848012, "either", false, 2508104241698097993], [15932120279885307830, "memchr", false, 18328080156587498744], [16066129441945555748, "bytes", false, 6700436516087834111], [16311359161338405624, "rustls_pemfile", false, 3525450195663376967], [16362055519698394275, "serde_json", false, 10885782402156260449], [16973251432615581304, "tokio_stream", false, 15383146493107249136], [17106256174509013259, "atoi", false, 12438799849597474653], [17531218394775549125, "tokio", false, 4132657057866398954], [17605717126308396068, "paste", false, 3298675644672636038], [17652733826348741533, "webpki_roots", false, 18258100460634043188]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-c09fb80d43d5517c\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}