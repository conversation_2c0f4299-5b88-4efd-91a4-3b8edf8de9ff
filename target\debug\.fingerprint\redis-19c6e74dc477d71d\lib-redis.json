{"rustc": 1842507548689473721, "features": "[\"acl\", \"aio\", \"async-trait\", \"bytes\", \"default\", \"futures-util\", \"geospatial\", \"keep-alive\", \"pin-project-lite\", \"script\", \"sha1_smol\", \"socket2\", \"streams\", \"tokio\", \"tokio-comp\", \"tokio-util\"]", "declared_features": "[\"acl\", \"ahash\", \"aio\", \"arc-swap\", \"async-native-tls\", \"async-std\", \"async-std-comp\", \"async-std-native-tls-comp\", \"async-std-rustls-comp\", \"async-std-tls-comp\", \"async-trait\", \"bytes\", \"cluster\", \"cluster-async\", \"connection-manager\", \"crc16\", \"default\", \"futures\", \"futures-rustls\", \"futures-util\", \"geospatial\", \"json\", \"keep-alive\", \"log\", \"native-tls\", \"pin-project-lite\", \"r2d2\", \"rand\", \"rustls\", \"rustls-native-certs\", \"rustls-pemfile\", \"rustls-webpki\", \"script\", \"sentinel\", \"serde\", \"serde_json\", \"sha1_smol\", \"socket2\", \"streams\", \"tcp_nodelay\", \"tls\", \"tls-native-tls\", \"tls-rustls\", \"tls-rustls-insecure\", \"tls-rustls-webpki-roots\", \"tokio\", \"tokio-comp\", \"tokio-native-tls\", \"tokio-native-tls-comp\", \"tokio-retry\", \"tokio-rustls\", \"tokio-rustls-comp\", \"tokio-util\", \"webpki-roots\"]", "target": 5936222214539778515, "profile": 2241668132362809309, "path": 16294630538557387213, "deps": [[40386456601120721, "percent_encoding", false, 13044842269826592011], [917570942013697716, "sha1_smol", false, 4284314779465317296], [1211321333142909612, "socket2", false, 326810645036526811], [1216309103264968120, "ryu", false, 1279822531505251318], [1906322745568073236, "pin_project_lite", false, 7109322642501522252], [3150220818285335163, "url", false, 5124748645926582989], [7695812897323945497, "itoa", false, 8884530218275914875], [10629569228670356391, "futures_util", false, 9947495487474251517], [11946729385090170470, "async_trait", false, 16175769260559324859], [15894030960229394068, "tokio_util", false, 4290992106245206796], [16066129441945555748, "bytes", false, 6700436516087834111], [17531218394775549125, "tokio", false, 4132657057866398954], [17915660048393766120, "combine", false, 3490257766783000534]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\redis-19c6e74dc477d71d\\dep-lib-redis", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}