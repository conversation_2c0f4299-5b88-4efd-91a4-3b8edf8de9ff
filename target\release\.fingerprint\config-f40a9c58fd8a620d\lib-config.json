{"rustc": 1842507548689473721, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 11325340229998591071, "path": 13785040948118921715, "deps": [[1213098572879462490, "json5_rs", false, 6426773922821994334], [1965680986145237447, "yaml_rust2", false, 1744761175012571719], [2244620803250265856, "ron", false, 6158622707986659924], [6502365400774175331, "nom", false, 15820068375576791178], [6517602928339163454, "path<PERSON><PERSON>", false, 15661350364115762443], [9689903380558560274, "serde", false, 5616481245319993094], [11946729385090170470, "async_trait", false, 17291696389737625151], [13475460906694513802, "convert_case", false, 16512886490568491680], [14618892375165583068, "ini", false, 7655059559720486232], [15609422047640926750, "toml", false, 18439233026325557818], [16362055519698394275, "serde_json", false, 17317803994796681169]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\config-f40a9c58fd8a620d\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}