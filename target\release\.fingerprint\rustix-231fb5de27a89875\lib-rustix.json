{"rustc": 1842507548689473721, "features": "[\"alloc\", \"fs\", \"net\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 3558674219944592209, "path": 16740312582518946791, "deps": [[7263319592666514104, "windows_sys", false, 17090778663750816370], [7896293946984509699, "bitflags", false, 4620450950774437720], [8253628577145923712, "libc_errno", false, 16791064381683928455], [10004434995811528692, "build_script_build", false, 10999016664558849248]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustix-231fb5de27a89875\\dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}