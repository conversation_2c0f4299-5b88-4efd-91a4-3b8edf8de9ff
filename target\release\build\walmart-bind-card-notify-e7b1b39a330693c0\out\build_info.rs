
// 自动生成的构建信息 - 请勿手动修改

pub const BUILD_TARGET: &str = "x86_64-pc-windows-msvc";
pub const BUILD_PROFILE: &str = "release";
pub const BUILD_TIMESTAMP: i64 = 1754407502;
pub const GIT_HASH: &str = "unknown";
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");

pub fn get_build_info() -> String {
    format!(
        "{} v{} ({} built for {} at {}",
        NAME,
        VERSION,
        GIT_HASH,
        BUILD_TARGET,
        BUILD_TIMESTAMP
    )
}
